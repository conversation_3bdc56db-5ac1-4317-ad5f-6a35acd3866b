using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

/// <summary>
/// Handler for schedule synchronization commands
/// </summary>
public interface ISynchronizeSchedulesCommandHandler
{
    /// <summary>
    /// Handles the synchronization of journal schedules with APIFY
    /// </summary>
    /// <param name="command">The synchronization command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Synchronization result</returns>
    Task<ScheduleSynchronizationResult> Consume(SynchronizeSchedulesCommand command, CancellationToken cancellationToken = default);
}

/// <summary>
/// Handler for single journal schedule synchronization commands
/// </summary>
public interface ISynchronizeJournalScheduleCommandHandler
{
    /// <summary>
    /// Handles the synchronization of a single journal schedule
    /// </summary>
    /// <param name="command">The journal synchronization command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Journal synchronization result</returns>
    Task<JournalSynchronizationResult> Consume(SynchronizeJournalScheduleCommand command, CancellationToken cancellationToken = default);
}

/// <summary>
/// Handler for schedule change detection commands
/// </summary>
public interface IDetectScheduleChangesCommandHandler
{
    /// <summary>
    /// Handles the detection of schedule changes
    /// </summary>
    /// <param name="command">The change detection command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Change detection result</returns>
    Task<ScheduleChangeDetectionResult> Consume(DetectScheduleChangesCommand command, CancellationToken cancellationToken = default);
}

/// <summary>
/// Handler for duplicate schedule validation commands
/// </summary>
public interface IValidateDuplicateSchedulesCommandHandler
{
    /// <summary>
    /// Handles the validation of duplicate schedules
    /// </summary>
    /// <param name="command">The validation command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Duplicate validation result</returns>
    Task<DuplicateValidationResult> Consume(ValidateDuplicateSchedulesCommand command, CancellationToken cancellationToken = default);
}

/// <summary>
/// Handler for orphaned schedule cleanup commands
/// </summary>
public interface ICleanupOrphanedSchedulesCommandHandler
{
    /// <summary>
    /// Handles the cleanup of orphaned schedules
    /// </summary>
    /// <param name="command">The cleanup command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cleanup result</returns>
    Task<ScheduleCleanupResult> Consume(CleanupOrphanedSchedulesCommand command, CancellationToken cancellationToken = default);
}
