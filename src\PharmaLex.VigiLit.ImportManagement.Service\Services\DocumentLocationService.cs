﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ImportManagement.Documents;
using System.Net;
using System.Text;
using System.Text.Json;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

internal class DocumentLocationService : IDocumentLocationService
{
    private readonly IImportFileDocumentService _importFileDocumentService;
    private readonly ILogger<DocumentLocationService> _logger;

    public DocumentLocationService(IImportFileDocumentService importFileDocumentService,
                                   ILogger<DocumentLocationService> logger)
    {
        _importFileDocumentService = importFileDocumentService;
        _logger = logger;
    }

    public async Task<string> GetTextFromDocument(string documentLocation, string element)
    {
        try
        {
            var jsonElement = JsonSerializer.Deserialize<JsonElement>(documentLocation);

            var batchFound = jsonElement.GetProperty("BatchId").TryGetGuid(out Guid batchId);
            var fileNameFound = jsonElement.TryGetProperty(element, out JsonElement fileName);

            if (batchFound && fileNameFound && !string.IsNullOrEmpty(fileName.ToString()))
            {
                return await GetRawTextFromBlob(batchId, fileName.ToString());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Document location {DocumentLocation} could not be parsed or document could not be loaded", documentLocation);
        }
        return "";
    }

    private async Task<string> GetRawTextFromBlob(Guid batchId, string filename)
    {
        var importFileDocumentDescriptor = new ImportFileDescriptor(batchId, filename);

        try
        {
            var stream = await _importFileDocumentService.OpenRead(importFileDocumentDescriptor);

            using (var reader = new StreamReader(stream, Encoding.UTF8))
            {
                return await reader.ReadToEndAsync();
            }
        }
        catch (Azure.RequestFailedException ex)
        {
            if (ex.Status == (int)HttpStatusCode.Unauthorized)
            {
                Console.WriteLine("Developer: if you have reached this, make sure \"AppSettings:EnvironmentName\": \"local\" is in your secrets ");
            }
            _logger.LogError(ex, "Authorisation error: blob {BatchId}/{Filename} could not be loaded", batchId, filename);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Blob {BatchId}/{Filename} could not be loaded", batchId, filename);
        }
        return "";
    }
}
