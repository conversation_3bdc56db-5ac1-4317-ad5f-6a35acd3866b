﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Helpers;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services
{
    public class CreateOrUpdateScheduleCommandHandler : ICreateOrUpdateScheduleCommandHandler
    {
        private readonly ILogger<CreateOrUpdateScheduleCommandHandler> _logger;
        private readonly IApifyTaskService _taskService;
        private readonly IApifyScheduleService _scheduleService;
        private readonly IApifyWebhookService _webhookService;
        private readonly IScrapingConfigurationService _configurationService;

        public CreateOrUpdateScheduleCommandHandler(
            ILogger<CreateOrUpdateScheduleCommandHandler> logger,
            IApifyTaskService taskService,
            IApifyScheduleService scheduleService,
            IApifyWebhookService webhookService,
            IScrapingConfigurationService configurationService)
        {
            _logger = logger;
            _taskService = taskService;
            _scheduleService = scheduleService;
            _webhookService = webhookService;
            _configurationService = configurationService;
        }

        public async Task Consume(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("CreateOrUpdateScheduleCommandHandler:Consume:{Journal}", LogSanitizer.Sanitize(command.NewJournal.Name));
            try
            {
                if (command.OperationType == JournalOperationType.Create)
                {
                    await OnJournalAddedAsync(command, cancellationToken);
                }
                else if (command.OperationType == JournalOperationType.Update)
                {
                    await OnJournalUpdatedAsync(command, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to create or update Apify schedule: {ex.Message}";
                _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                throw new InvalidOperationException(errorMessage, ex);
            }

            _logger.LogInformation("CreateOrUpdateJournalCommandHandler:Consume: Completed.");
        }

        private async Task OnJournalAddedAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var schedule = schedules?.Data.Items.Find(x => x.CronExpression == command.NewJournal.CronExpression);
            string? taskId;
            if (schedule != null)
            {
                taskId = await UpdateTaskAsync(command.NewJournal.Url, null, schedule, cancellationToken);
                if (String.IsNullOrEmpty(taskId))
                {
                    taskId = await CreateTaskAsync(command, cancellationToken);
                    await _scheduleService.UpdateScheduleAsync(schedule.Id, taskId!, cancellationToken);
                }
            }
            else
            {
                taskId = await CreateScheduleWithTaskAsync(command, cancellationToken);
            }
            await UpdateJournalWithTaskIdAsync(command, taskId);
        }

        private async Task OnJournalUpdatedAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            string? taskId = null;
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
           
            if (command.NewJournal.CronExpression != command.OldJournal!.CronExpression) 
            {
                taskId = await CreateOrUpdateScheduleAsync(command, schedules, cancellationToken);
            }
            else 
            {
                if (command.NewJournal.Url != command.OldJournal!.Url)
                {
                    taskId = await OnUrlChanged(command, schedules, cancellationToken);
                }
            }

            await UpdateJournalWithTaskIdAsync(command, taskId);
        }

        private async Task<string?> OnUrlChanged(CreateOrUpdateScheduleCommand command, Apify.SDK.Model.GetListOfSchedulesResponse schedules, CancellationToken cancellationToken)
        {
            string? taskId = null;
            var oldSchedule = schedules?.Data.Items.Find(x => x.CronExpression == command.OldJournal!.CronExpression);
            if (oldSchedule == null)
            {
                taskId = await CreateScheduleWithTaskAsync(command, cancellationToken);
            }
            else
            {
                taskId = await UpdateOrCreateTaskAsync(command, oldSchedule, cancellationToken);
            }

            return taskId;
        }

        private async Task<string?> UpdateOrCreateTaskAsync(CreateOrUpdateScheduleCommand command, Apify.SDK.Model.GetListOfSchedulesResponseDataItems? oldSchedule, CancellationToken cancellationToken)
        {
            string? taskId = command.OldJournal!.TaskId;
            if (!String.IsNullOrEmpty(taskId))
            {
                await _taskService.UpdateTaskStartUrlsAsync(taskId, oldSchedule!.Id, command.NewJournal.Url, command.OldJournal.Url, cancellationToken);
            }
            else
            {
                taskId = await UpdateTaskAsync(command.NewJournal.Url, command.OldJournal.Url, oldSchedule, cancellationToken);
                if (String.IsNullOrEmpty(taskId))
                {
                    taskId = await CreateTaskAsync(command, cancellationToken);
                    await _scheduleService.UpdateScheduleAsync(oldSchedule!.Id, taskId!, cancellationToken);
                }
            }

            return taskId;
        }

        private async Task<string?> CreateOrUpdateScheduleAsync(CreateOrUpdateScheduleCommand command,
            Apify.SDK.Model.GetListOfSchedulesResponse? schedules, CancellationToken cancellationToken = default)
        {
            await RemoveUrlFromTaskAsync(command, schedules, cancellationToken);
            var schedule = schedules?.Data.Items.Find(x => x.CronExpression == command.NewJournal.CronExpression);
            if (schedule != null)
            {
                return await UpdateTaskAsync(command.NewJournal.Url, null, schedule, cancellationToken);
            }
            else
            {
                return await CreateScheduleWithTaskAsync(command, cancellationToken);
            }
        }

        private async Task<string?> CreateScheduleWithTaskAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            var taskId = await CreateTaskAsync(command, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                return null;
            }
            var scheduleName = ScrapingHelper.GenerateUniqueName("schedule", command.NewJournal.CronExpression);
            await _scheduleService.CreateScheduleForTaskAsync(taskId, scheduleName, command.NewJournal.CronExpression, cancellationToken);

            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                var errorMessage = $"Failed to create task for journal '{command.NewJournal.Name}': Webhook URL is missing";
                _logger.LogWarning(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
            }
            await _webhookService.CreateWebhookForTaskAsync(taskId, webhookUrl, cancellationToken);
            return taskId;
        }

        private async Task RemoveUrlFromTaskAsync(CreateOrUpdateScheduleCommand command, Apify.SDK.Model.GetListOfSchedulesResponse? schedules, CancellationToken cancellationToken = default)
        {
            var oldSchedule = schedules?.Data.Items.Find(x => x.CronExpression == command.OldJournal!.CronExpression);
            if (oldSchedule != null)
            {
                await _taskService.UpdateTaskStartUrlsAsync(command.OldJournal!.TaskId!, oldSchedule.Id, null, command.OldJournal.Url, cancellationToken);
            }
        }

        private async Task<string?> UpdateTaskAsync(string newUrl, string? oldUrl, Apify.SDK.Model.GetListOfSchedulesResponseDataItems? schedule, CancellationToken cancellationToken = default)
        {
            var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(schedule!.Id, cancellationToken);
            if (taskIds == null || taskIds.Count == 0 || string.IsNullOrEmpty(taskIds[0]))
            {
                var errorMessage = $"Failed to get taskId for schedule '{schedule.Id}': Task ID was null or empty";
                _logger.LogWarning(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                return null;
            }
            else
            {
                await _taskService.UpdateTaskStartUrlsAsync(taskIds[0], schedule.Id, newUrl, oldUrl, cancellationToken);
                return taskIds[0];
            }
        }

        private async Task<string?> CreateTaskAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            var taskName = $"vigilit-{ScrapingHelper.SanitizeApifyName(command.NewJournal.Name)}-{command.NewJournal.Id}";
            var journal = new JournalScheduleInfo()
            {
                Id = command.NewJournal.Id,
                Name = command.NewJournal.Name,
                Url = command.NewJournal.Url,
                CronExpression = command.NewJournal.CronExpression
            };

            var taskId = await _taskService.CreateTaskForJournalAsync(journal, taskName, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                var errorMessage = $"Failed to create task for journal '{command.NewJournal.Name}': Task ID was null or empty";
                _logger.LogWarning(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                return null;
            }
            return taskId;
        }

        private static async Task UpdateJournalWithTaskIdAsync(CreateOrUpdateScheduleCommand command, string? taskId)
        {
            if (command.UpdateJournalWithTaskId != null && taskId != null)
            {
                await command.UpdateJournalWithTaskId(taskId, command.NewJournal.Id);
            }
        }
    }
}
