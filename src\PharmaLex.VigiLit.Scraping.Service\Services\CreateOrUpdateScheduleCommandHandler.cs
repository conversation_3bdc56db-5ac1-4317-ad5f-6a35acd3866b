﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class CreateOrUpdateScheduleCommandHandler : ICreateOrUpdateScheduleCommandHandler
{
    private readonly ILogger<CreateOrUpdateScheduleCommandHandler> _logger;
    private readonly IScheduleManagementService _scheduleManagementService;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyScheduleService _scheduleService;

    public CreateOrUpdateScheduleCommandHandler(
        ILogger<CreateOrUpdateScheduleCommandHandler> logger,
        IScheduleManagementService scheduleManagementService,
        IApifyTaskService taskService,
        IApifyScheduleService scheduleService)
    {
        _logger = logger;
        _scheduleManagementService = scheduleManagementService;
        _taskService = taskService;
        _scheduleService = scheduleService;
    }

    public async Task Consume(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("CreateOrUpdateScheduleCommandHandler:Consume:{Journal}", LogSanitizer.Sanitize(command.NewJournal.Name));
        try
        {
            if (command.OperationType == JournalOperationType.Create)
            {
                await OnJournalAddedAsync(command, cancellationToken);
            }
            else if (command.OperationType == JournalOperationType.Update)
            {
                await OnJournalUpdatedAsync(command, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to create or update Apify schedule: {ex.Message}";
            _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
            throw new InvalidOperationException(errorMessage, ex);
        }

        _logger.LogInformation("CreateOrUpdateJournalCommandHandler:Consume: Completed.");
    }

    private async Task OnJournalAddedAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
    {
        var existingSchedule = await _scheduleManagementService.FindExistingScheduleAsync(command.NewJournal.CronExpression, cancellationToken);
        string? taskId;

        if (existingSchedule != null)
        {
            taskId = await _scheduleManagementService.UpdateScheduleTaskAsync(existingSchedule, command.NewJournal.Url, null, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                taskId = await _scheduleManagementService.CreateTaskForJournalAsync(command.NewJournal, cancellationToken);
                if (!string.IsNullOrEmpty(taskId))
                {
                    await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, taskId, cancellationToken);
                    await _scheduleManagementService.CreateWebhookIfConfiguredAsync(taskId, cancellationToken);
                }
            }
        }
        else
        {
            taskId = await _scheduleManagementService.CreateScheduleWithTaskAsync(command.NewJournal, cancellationToken);
        }

        await UpdateJournalWithTaskIdAsync(command, taskId);
    }

    private async Task OnJournalUpdatedAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
    {
        string? taskId = null;

        if (command.NewJournal.CronExpression != command.OldJournal!.CronExpression)
        {
            taskId = await HandleCronExpressionChangeAsync(command, cancellationToken);
        }
        else
        {
            if (command.NewJournal.Url != command.OldJournal!.Url)
            {
                taskId = await HandleUrlChangeAsync(command, cancellationToken);
            }
        }

        await UpdateJournalWithTaskIdAsync(command, taskId);
    }

    private async Task<string?> HandleUrlChangeAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken)
    {
        var oldSchedule = await _scheduleManagementService.FindExistingScheduleAsync(command.OldJournal!.CronExpression, cancellationToken);
        if (oldSchedule == null)
        {
            return await _scheduleManagementService.CreateScheduleWithTaskAsync(command.NewJournal, cancellationToken);
        }
        else
        {
            return await UpdateOrCreateTaskAsync(command, oldSchedule, cancellationToken);
        }
    }

    private async Task<string?> UpdateOrCreateTaskAsync(CreateOrUpdateScheduleCommand command, Apify.SDK.Model.GetListOfSchedulesResponseDataItems oldSchedule, CancellationToken cancellationToken)
    {
        string? taskId = command.OldJournal!.TaskId;
        if (!string.IsNullOrEmpty(taskId))
        {
            await _taskService.UpdateTaskStartUrlsAsync(taskId, oldSchedule.Id, command.NewJournal.Url, command.OldJournal.Url, cancellationToken);
            return taskId;
        }
        else
        {
            taskId = await _scheduleManagementService.UpdateScheduleTaskAsync(oldSchedule, command.NewJournal.Url, command.OldJournal.Url, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                taskId = await _scheduleManagementService.CreateTaskForJournalAsync(command.NewJournal, cancellationToken);
                if (!string.IsNullOrEmpty(taskId))
                {
                    await _scheduleService.UpdateScheduleAsync(oldSchedule.Id, taskId, cancellationToken);
                    await _scheduleManagementService.CreateWebhookIfConfiguredAsync(taskId, cancellationToken);
                }
            }
            return taskId;
        }
    }

    private async Task<string?> HandleCronExpressionChangeAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken)
    {
        await RemoveUrlFromOldScheduleAsync(command, cancellationToken);

        var newSchedule = await _scheduleManagementService.FindExistingScheduleAsync(command.NewJournal.CronExpression, cancellationToken);
        if (newSchedule != null)
        {
            return await _scheduleManagementService.UpdateScheduleTaskAsync(newSchedule, command.NewJournal.Url, null, cancellationToken);
        }
        else
        {
            return await _scheduleManagementService.CreateScheduleWithTaskAsync(command.NewJournal, cancellationToken);
        }
    }

    private async Task RemoveUrlFromOldScheduleAsync(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(command.OldJournal?.TaskId) || string.IsNullOrEmpty(command.OldJournal?.CronExpression))
        {
            return;
        }

        var oldSchedule = await _scheduleManagementService.FindExistingScheduleAsync(command.OldJournal.CronExpression, cancellationToken);
        if (oldSchedule != null)
        {
            await _taskService.UpdateTaskStartUrlsAsync(command.OldJournal.TaskId, oldSchedule.Id, null, command.OldJournal.Url, cancellationToken);
        }
    }

    private static async Task UpdateJournalWithTaskIdAsync(CreateOrUpdateScheduleCommand command, string? taskId)
    {
        if (command.UpdateJournalWithTaskId != null && taskId != null)
        {
            await command.UpdateJournalWithTaskId(taskId, command.NewJournal.Id);
        }
    }
}
