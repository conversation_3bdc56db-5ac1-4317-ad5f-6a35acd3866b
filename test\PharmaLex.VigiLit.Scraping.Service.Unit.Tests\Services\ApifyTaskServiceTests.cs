using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Scraping.Service.Services;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class ApifyTaskServiceTests
{
    private readonly Mock<IApifyClient> _mockApifyClient;
    private readonly Mock<ILogger<ApifyTaskService>> _mockLogger;
    private readonly ApifyTaskService _service;

    public ApifyTaskServiceTests()
    {
        _mockApifyClient = new Mock<IApifyClient>();
        _mockLogger = new Mock<ILogger<ApifyTaskService>>();
        _service = new ApifyTaskService(_mockApifyClient.Object, _mockLogger.Object);
    }

    // Existing tests for other methods would be here...

    [Fact]
    public async Task UpdateTaskStartUrlsAsync_WithValidParameters_CallsClientAndLogs()
    {
        // Arrange
        var actorTaskId = "test-task-123";
        var scheduleId = "test-schedule-456";
        var addUrl = "https://new.example.com";
        var removeUrl = "https://old.example.com";

        // Act
        await _service.UpdateTaskStartUrlsAsync(actorTaskId, scheduleId, addUrl, removeUrl, CancellationToken.None);

        // Assert
        _mockApifyClient.Verify(x => x.UpdateTaskStartUrlsAsync(
            actorTaskId, scheduleId, addUrl, removeUrl, It.IsAny<CancellationToken>()),
            Times.Once);

        VerifyLogCalled(LogLevel.Information, $"Updated start Urls of task '{actorTaskId}'");
    }

    [Fact]
    public async Task UpdateTaskStartUrlsAsync_WithNullScheduleId_CallsClientWithNull()
    {
        // Arrange
        var actorTaskId = "test-task-123";
        var addUrl = "https://new.example.com";
        var removeUrl = "https://old.example.com";

        // Act
        await _service.UpdateTaskStartUrlsAsync(actorTaskId, null, addUrl, removeUrl, CancellationToken.None);

        // Assert
        _mockApifyClient.Verify(x => x.UpdateTaskStartUrlsAsync(
            actorTaskId, null, addUrl, removeUrl, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UpdateTaskStartUrlsAsync_WithNullUrls_CallsClientWithNull()
    {
        // Arrange
        var actorTaskId = "test-task-123";
        var scheduleId = "test-schedule-456";

        // Act
        await _service.UpdateTaskStartUrlsAsync(actorTaskId, scheduleId, null, null, CancellationToken.None);

        // Assert
        _mockApifyClient.Verify(x => x.UpdateTaskStartUrlsAsync(
            actorTaskId, scheduleId, null, null, It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task UpdateTaskStartUrlsAsync_WhenClientThrows_LogsErrorAndThrows()
    {
        // Arrange
        var actorTaskId = "test-task-123";
        var scheduleId = "test-schedule-456";
        var addUrl = "https://new.example.com";
        var removeUrl = "https://old.example.com";
        var exception = new Exception("Test exception");

        _mockApifyClient.Setup(x => x.UpdateTaskStartUrlsAsync(
            actorTaskId, scheduleId, addUrl, removeUrl, It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.UpdateTaskStartUrlsAsync(actorTaskId, scheduleId, addUrl, removeUrl, CancellationToken.None));

        Assert.Equal($"Failed to update start Urls of task '{actorTaskId}'", ex.Message);
        Assert.Same(exception, ex.InnerException);

        VerifyLogCalled(LogLevel.Error, $"Failed to update start Urls of task '{actorTaskId}'");
    }

    [Fact]
    public async Task GetTaskIdsByScheduleIdAsync_WithValidScheduleId_ReturnsTaskIds()
    {
        // Arrange
        var scheduleId = "test-schedule-456";
        var expectedTaskIds = new List<string> { "task-1", "task-2" };

        _mockApifyClient.Setup(x => x.GetTaskIdsByScheduleIdAsync(scheduleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedTaskIds);

        // Act
        var result = await _service.GetTaskIdsByScheduleIdAsync(scheduleId, CancellationToken.None);

        // Assert
        Assert.Equal(expectedTaskIds, result);
        _mockApifyClient.Verify(x => x.GetTaskIdsByScheduleIdAsync(
            scheduleId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetTaskIdsByScheduleIdAsync_WhenClientReturnsNull_ReturnsNull()
    {
        // Arrange
        var scheduleId = "test-schedule-456";

        _mockApifyClient.Setup(x => x.GetTaskIdsByScheduleIdAsync(scheduleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((List<string>?)null);

        // Act
        var result = await _service.GetTaskIdsByScheduleIdAsync(scheduleId, CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetTaskIdsByScheduleIdAsync_WhenClientThrows_LogsErrorAndThrows()
    {
        // Arrange
        var scheduleId = "test-schedule-456";
        var exception = new Exception("Test exception");

        _mockApifyClient.Setup(x => x.GetTaskIdsByScheduleIdAsync(
            scheduleId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(exception);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _service.GetTaskIdsByScheduleIdAsync(scheduleId, CancellationToken.None));

        Assert.Equal($"Failed to get taskId for schedule '{scheduleId}'", ex.Message);
        Assert.Same(exception, ex.InnerException);

        VerifyLogCalled(LogLevel.Error, $"Failed to get taskId for schedule '{scheduleId}'");
    }

    private void VerifyLogCalled(LogLevel level, string message)
    {
        _mockLogger.Verify(
            x => x.Log(
                level,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }
}