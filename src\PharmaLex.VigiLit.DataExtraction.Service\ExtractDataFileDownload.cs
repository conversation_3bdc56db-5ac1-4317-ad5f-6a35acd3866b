﻿using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Import;
using PharmaLex.VigiLit.DataExtraction.Service.Extensions;

namespace PharmaLex.VigiLit.DataExtraction.Service;

internal class ExtractDataFileDownload : IExtractDataFileDownload
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageImportFileDocumentOptions _importFileDocumentOptions;
    private readonly AzureStorageImportFileDocumentUploadOptions _importFileDocumentUploadOptions;

    public ExtractDataFileDownload(
        IDocumentService documentService,
        IOptions<AzureStorageImportFileDocumentUploadOptions> importFileImportDocumentUploadOptions,
        IOptions<AzureStorageImportFileDocumentOptions> importFileImportDocumentOptions)
    {
        _documentService = documentService;
        _importFileDocumentOptions = importFileImportDocumentOptions.Value;
        _importFileDocumentUploadOptions = importFileImportDocumentUploadOptions.Value;
    }

    public async Task<DownloadFile> DownloadImportedFile(Guid batchId, string fileName)
    {
        var pdfBytes = await RetrieveDocument(batchId, fileName);
        return new DownloadFile
        {
            FileName = fileName,
            ContentType = "application/pdf",
            Bytes = pdfBytes
        };
    }

    private async Task<byte[]> RetrieveDocument(Guid batchId, string fileName)
    {
        var importFileDocumentUploadDescriptor = new ImportFileDescriptor(batchId, fileName);
        using var stream = await OpenRead(importFileDocumentUploadDescriptor);

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);

        return memoryStream.ToArray();
    }


    private async Task<Stream> OpenRead(ImportFileDescriptor importFileDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(importFileDocumentDescriptor);
        var stream = await _documentService.OpenRead(documentDescriptor, cancellationToken);
        return stream;
    }

    public async Task<bool> Exists<T>(T fileDescriptor, CancellationToken cancellationToken = default)
    {
        DocumentDescriptor documentDescriptor = fileDescriptor switch
        {
            ImportFileUploadDescriptor uploadDescriptor => GetDocumentDescriptor(uploadDescriptor),
            ImportFileDescriptor importFileDescriptor => GetDocumentDescriptor(importFileDescriptor),
            _ => throw new ArgumentException($"Unsupported type: {typeof(T).Name}")
        };
        var exists = await _documentService.Exists(documentDescriptor, cancellationToken);
        return exists;
    }


    public async Task<DocumentProperties> Copy(ImportFileUploadDescriptor importFileUploadDescriptor, ImportFileDescriptor importFileDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(importFileUploadDescriptor);
        var documentDescriptor = GetDocumentDescriptor(importFileDescriptor);
        var documentProperties = await _documentService.CopyFrom(documentDescriptorUpload, documentDescriptor, cancellationToken);
        return documentProperties;
    }


    public async Task<DocumentProperties> CopyBack(
        ImportFileDescriptor importFileDescriptor,
        ImportFileUploadDescriptor importFileUploadDescriptor,
        CancellationToken cancellationToken = default)
    {
        var sourceDocumentDescriptor = GetDocumentDescriptor(importFileDescriptor);
        var destinationDocumentDescriptor = GetDocumentDescriptor(importFileUploadDescriptor);
        var documentProperties = await _documentService.CopyFrom(sourceDocumentDescriptor, destinationDocumentDescriptor, cancellationToken);
        return documentProperties;
    }

    public async Task Delete<T>(T fileDescriptor, CancellationToken cancellationToken = default)
    {
        DocumentDescriptor documentDescriptor = fileDescriptor switch
        {
            ImportFileUploadDescriptor uploadDescriptor => GetDocumentDescriptor(uploadDescriptor),
            ImportFileDescriptor importFileDescriptor => GetDocumentDescriptor(importFileDescriptor),
            _ => throw new ArgumentException($"Unsupported type: {typeof(T).Name}")
        };
        await _documentService.Delete(documentDescriptor, cancellationToken);
    }

    public async Task CreateTextBlob(Guid batchId, string fileName, string text)
    {
        var importFileDocumentDescriptor = new ImportFileDescriptor(batchId, fileName);

        await Create(importFileDocumentDescriptor, text.ToStream());
    }

    public async Task<BlobStoragePath> CreateTranslationBlobsAndGetPath(Guid batchId, string fileName, ContextInfo contextInfo)
    {
        var baseFileName = Path.GetFileNameWithoutExtension(fileName);
        var translatedFileName = $"{baseFileName}_TranslatedText.txt";
        var untranslatedFileName = $"{baseFileName}_UntranslatedText.txt";

        var translatedDescriptor = new ImportFileDescriptor(batchId, translatedFileName);
        var untranslatedDescriptor = new ImportFileDescriptor(batchId, untranslatedFileName);

        await Create(translatedDescriptor, contextInfo.RawTranslatedText.ToStream());
        await Create(untranslatedDescriptor, contextInfo.RawUnTranslatedText.ToStream());

        return new BlobStoragePath
        {
            Container = _importFileDocumentOptions.ContainerName,
            BatchId = batchId,
            FileName = fileName,
            TranslatedFileName = translatedFileName,
            UntranslatedFileName = untranslatedFileName
        };
    }

    private async Task Create(ImportFileDescriptor importFileDescriptor, Stream stream, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(importFileDescriptor);
        stream.Position = 0;
        await _documentService.Create(documentDescriptorUpload, stream, cancellationToken);
    }

    private DocumentDescriptor GetDocumentDescriptor(ImportFileDescriptor importFileDescriptor)
    {
        var blobName = importFileDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_importFileDocumentOptions.AccountName, _importFileDocumentOptions.ContainerName, blobName);
        return documentDescriptor;
    }

    private DocumentDescriptor GetDocumentDescriptor(ImportFileUploadDescriptor importFileUploadDescriptor)
    {
        var blobName = importFileUploadDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_importFileDocumentUploadOptions.AccountName, _importFileDocumentUploadOptions.ContainerName, blobName);
        return documentDescriptor;
    }
}