﻿using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Infrastructure.Emails;

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly IEmailSender _emailSender;
    private readonly ICompanyRepository _companyRepository;
    private readonly IEmailLogService _emailLogService;
    private readonly ICompanyInterestRepository _companyInterestRepository;
    private readonly IReferenceRepository _referenceRepository;
    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly IEmailRepository _emailRepository;
    private readonly IUrlRenderHelper _urlRenderHelper;

    public EmailService(
        ILoggerFactory loggerFactory,
        IEmailSender sender,
        ICompanyRepository companyRepository,
        IEmailLogService emailLogService,
        ICompanyInterestRepository companyInterestRepository,
        IReferenceRepository referenceRepository,
        IReferenceClassificationRepository referenceClassificationRepository,
        IEmailRepository emailRepository,
        IUrlRenderHelper urlRenderHelper
        )
    {
        _logger = loggerFactory.CreateLogger<EmailService>();
        _emailSender = sender;
        _companyRepository = companyRepository;
        _emailLogService = emailLogService;
        _companyInterestRepository = companyInterestRepository;
        _referenceRepository = referenceRepository;
        _referenceClassificationRepository = referenceClassificationRepository;
        _emailRepository = emailRepository;
        _urlRenderHelper = urlRenderHelper;
    }

    [Trace]
    public async Task SendDailyReferenceClassificationEmails(EmailTriggerType emailTriggerType)
    {
        var email = await _emailLogService.CreateAndSaveEmailLog(EmailType.DailyReferenceClassificationEmail, emailTriggerType);

        var sentEmailsCount = 0;
        var failedEmailsCount = 0;

        try
        {
            // get active companies
            var companies = await _companyRepository.GetActiveCompaniesWithUsers();
            _logger.LogInformation("Found {Count} companies to email.", companies.Count());

            // loop through active companies
            foreach (var company in companies)
            {
                _logger.LogInformation("Processing company. Name: {Name}, Id: {Id}.", company.Name, company.Id);

                // get company interests with new events
                var companyInterests = await _companyInterestRepository.GetForSendingDailyEmail(company.Id);
                _logger.LogInformation("Found {Count} company interests with new actions for company {Name}", companyInterests.Count(), company.Name);

                // define the email sections
                var emailSections = new Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>
                {
                    { EmailReason.NewPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() },
                    { EmailReason.ChangedFromPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() }
                };

                // loop through company interests
                await DetermineCompaniesEmailSections(companyInterests, emailSections);

                // sort the email sections by substance name then by plxid
                foreach (var key in emailSections.Keys)
                {
                    emailSections[key] = emailSections[key]
                        .OrderBy(m => m.ReferenceClassification.SubstanceName)
                        .ThenBy(m => m.ReferenceClassification.Id)
                        .ToList();
                }

                // sanitise the email sections (truncate abstract and add link)
                foreach (var key in emailSections.Keys)
                {
                    emailSections[key] = SanitiseModelsForEmail(emailSections[key]);
                }

                // if there are lots of rows then split into multiple emails
                var parts = GetEmailParts(emailSections);

                // send email (each company user, must be active, must have email preference)
                foreach (var user in company.CompanyUsers.Where(u => u.Active && u.EmailPreferenceIds.Contains((int)EmailType.DailyReferenceClassificationEmail)))
                {
                    _logger.LogInformation("Sending Daily Reference email for company {CompanyName} to recipient {RecipientName} at {RecipientEmail}.", company.Name, user.DisplayFullName, user.Email);

                    // the email might be in multiple parts
                    foreach (var part in parts)
                    {
                        // count the rows in the email
                        var emailRowCount = 0;
                        foreach (var key in part.Value.Keys)
                        {
                            emailRowCount += part.Value[key].Count;
                        }

                        // construct the email subject
                        var multipart = parts.Count > 1 ? $" ({part.Key}/{parts.Count})" : "";
                        var subject = string.Format("{0} VigiLit Classification{1} ({2}){3}", emailRowCount, emailRowCount == 1 ? "" : "s", DateTime.UtcNow.ToString("d MMM yyyy"), multipart);

                        // email is sent every day even if there are no classifications
                        var templateData = new DailyClassificationEmailSendGridTemplateModel
                        {
                            RecipientName = user.DisplayFullName,
                            RecipientEmail = user.Email,
                            Subject = subject,
                            NewPotentialCases = part.Value[EmailReason.NewPotentialCase],
                            ChangedFromPotentialCases = part.Value[EmailReason.ChangedFromPotentialCase]
                        };

                        _logger.LogInformation("Attempting to send email for company: {Name}, company id: {CompanyId} to recipient: {Email} with subject: '{Subject}'", company.Name, company.Id.ToString(), user.Email, subject);

                        // call send grid
                        var response = await _emailSender.SendDailyReferenceClassificationEmail(templateData);

                        // count success or failure
                        _ = response.IsSuccessStatusCode ? sentEmailsCount++ : failedEmailsCount++;

                        // log email message
                        var emailMessage = new EmailMessage()
                        {
                            CompanyId = company.Id,
                            UserId = user.Id,
                            EmailAddress = templateData.RecipientEmail,
                            Subject = templateData.Subject,
                            EmailMessageStatusType = response.IsSuccessStatusCode ? EmailMessageStatusType.Sent : EmailMessageStatusType.Failed,
                            Timestamp = DateTime.UtcNow
                        };
                        AddRelevantEvents(emailMessage, part);
                        email.EmailMessages.Add(emailMessage);
                    }
                }

                // save changes
                await _companyInterestRepository.SaveChangesAsync();
                await _emailRepository.SaveChangesAsync();

                // clear change tracker (the company is the batch size)
                _companyInterestRepository.ClearChangeTracker();
                _emailRepository.ClearChangeTracker();

                // reload the email to continue
                email = await _emailRepository.GetEmail(email.Id);
            }

            await _emailLogService.LogOutcome(email, failedEmailsCount == 0 ? EmailStatusType.Completed : EmailStatusType.CompletedWithFailedEmails, sentEmailsCount, failedEmailsCount);
        }
        catch
        {
            await _emailLogService.LogOutcome(email, EmailStatusType.Failed, sentEmailsCount, failedEmailsCount);
            throw;
        }
    }

    private async Task DetermineCompaniesEmailSections(IEnumerable<CompanyInterest> companyInterests, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>> emailSections)
    {
        foreach (var companyInterest in companyInterests)
        {
            // to calculate the email reason we need to know:
            // - the last emailed event
            // - the latest new event
            var lastEmailedEvent = companyInterest.EmailRelevantEvents
                .OrderBy(e => e.Id)
                .LastOrDefault(ci => ci.EmailRelevantEventEmailStatusType == EmailRelevantEventEmailStatusType.Sent);
            var latestNewEvent = companyInterest.EmailRelevantEvents
                .OrderBy(e => e.Id)
                .LastOrDefault(ci => ci.EmailRelevantEventEmailStatusType == EmailRelevantEventEmailStatusType.New);

            // calculate email reason
            await GetEmailSectionsByLatestEvent(emailSections, companyInterest, lastEmailedEvent, latestNewEvent);

            // discard unneeded rows
            // if the latest new event did not require emailing (if its status is still New) then it will also be discarded
            companyInterest.EmailRelevantEvents
                .Where(e => e.EmailRelevantEventEmailStatusType == EmailRelevantEventEmailStatusType.New)
                .ToList()
                .ForEach(e => e.EmailRelevantEventEmailStatusType = EmailRelevantEventEmailStatusType.Discarded);
        }
    }

    private async Task GetEmailSectionsByLatestEvent(Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>> emailSections, CompanyInterest companyInterest, EmailRelevantEvent? lastEmailedEvent, EmailRelevantEvent? latestNewEvent)
    {
        if (latestNewEvent != null)
        {
            var wasPotentialCase = lastEmailedEvent != null && lastEmailedEvent.ClassificationCategory != null && lastEmailedEvent.ClassificationCategory.PushServiceRelevant;
            var isPotentialCase = latestNewEvent.ClassificationCategory != null && latestNewEvent.ClassificationCategory.PushServiceRelevant;

            if (lastEmailedEvent == null && isPotentialCase)
            {
                latestNewEvent.EmailReason = EmailReason.NewPotentialCase;
            }
            else if (!wasPotentialCase && isPotentialCase)
            {
                latestNewEvent.EmailReason = EmailReason.NewPotentialCase;
            }
            else if (wasPotentialCase && !isPotentialCase)
            {
                latestNewEvent.EmailReason = EmailReason.ChangedFromPotentialCase;
            }

            // if the event requires emailing then set status to sent and set sent date
            // this will only be saved after the email has actually sent
            if (latestNewEvent.EmailReason != EmailReason.None)
            {
                latestNewEvent.EmailRelevantEventEmailStatusType = EmailRelevantEventEmailStatusType.Sent;
                latestNewEvent.EmailSentDate = DateTime.UtcNow;
            }

            // add the row into the email in the appropriate section
            if (latestNewEvent.EmailReason != EmailReason.None)
            {
                // fetch the content for the email row
                var referenceSendGridModel = await _referenceRepository.GetForEmail(companyInterest.ReferenceId);
                var referenceClassificationSendGridModel = await _referenceClassificationRepository.GetForEmail(companyInterest.ReferenceClassificationId);

                var model = new DailyClassificationEmailSendGridTemplateModelRow(latestNewEvent.Id, latestNewEvent.EmailReason, referenceSendGridModel, referenceClassificationSendGridModel);

                emailSections[latestNewEvent.EmailReason].Add(model);
            }
        }
    }

    private static void AddRelevantEvents(EmailMessage emailMessage, KeyValuePair<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>> part)
    {
        foreach (var key in part.Value.Keys)
        {
            foreach (var row in part.Value[key])
            {
                emailMessage.EmailMessageRelevantEvents.Add(new EmailMessageRelevantEvent()
                {
                    EmailRelevantEventId = row.EmailRelevantEventId
                });
            }
        }
    }

    public Dictionary<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>> GetEmailParts(Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>> emailSections)
    {
        var parts = new Dictionary<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>>();
        AddNewEmailPart(parts);

        // build up parts in this order
        var emailReasons = new List<EmailReason>()
        {
            EmailReason.NewPotentialCase,
            EmailReason.ChangedFromPotentialCase
        };

        foreach (var emailReason in emailReasons)
        {
            foreach (var row in emailSections[emailReason])
            {
                parts.Last().Value[emailReason].Add(row);

                if (IsLastPartFull(parts))
                {
                    AddNewEmailPart(parts);
                }
            }
        }

        // if there are multiple parts and the last one is empty, remove it.
        // fixes a bug which adds an empty part when the number of rows is exactly divisible by 100.
        if (parts.Count > 1 && IsLastPartEmpty(parts))
        {
            parts.Remove(parts.Count - 1);
        }

        return parts;
    }

    private static bool IsLastPartFull(Dictionary<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>> parts)
    {
        var maxPartSize = 100;

        return GetLastPartSize(parts) >= maxPartSize;
    }

    private static bool IsLastPartEmpty(Dictionary<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>> parts)
    {
        return GetLastPartSize(parts) == 0;
    }

    private static int GetLastPartSize(Dictionary<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>> parts)
    {
        var partSize = 0;

        foreach (var key in parts.Last().Value.Keys)
        {
            partSize += parts.Last().Value[key].Count;
        }

        return partSize;
    }

    private static void AddNewEmailPart(Dictionary<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>> parts)
    {
        int partNumber = parts.Count + 1;

        parts.Add(
            partNumber,
            new Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>
            {
                { EmailReason.NewPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() },
                { EmailReason.ChangedFromPotentialCase, new List<DailyClassificationEmailSendGridTemplateModelRow>() }
            });
    }

    public async Task SendInvitationEmail(InvitationEmailModel email)
    {
        await _emailSender.SendInvitationEmail(email);
    }

    private List<DailyClassificationEmailSendGridTemplateModelRow> SanitiseModelsForEmail(List<DailyClassificationEmailSendGridTemplateModelRow> models)
    {
        foreach (var model in models)
        {
            model.Reference.Abstract = _urlRenderHelper.GetUrlLinkBasedOnSourceSystem(model);
            _logger.LogInformation("Details of reference abstract {ReferenceAbstract}.", model.Reference.Abstract);
        }
        return models;
    }
}