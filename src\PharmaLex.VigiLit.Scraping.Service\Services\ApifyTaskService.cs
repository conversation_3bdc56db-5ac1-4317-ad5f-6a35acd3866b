using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyTaskService : IApifyTaskService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyTaskService> _logger;

    public ApifyTaskService(IApifyClient apifyClient, ILogger<ApifyTaskService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task<string> CreateTaskForJournalAsync(JournalScheduleInfo journal, string taskName, CancellationToken cancellationToken = default)
    {
        try
        {
            var urls = new List<string> { journal.Url };
            var taskId = await _apifyClient.CreateTaskAsync(taskName, urls, 1, cancellationToken);

            _logger.LogInformation("Created Apify task '{TaskName}' with ID '{TaskId}' for journal '{JournalName}'",
                taskName, taskId, journal.Name);

            return taskId;
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify task '{taskName}' for journal '{journal.Name}' (ID: {journal.Id}, URL: {journal.Url})";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }

    public async Task UpdateTaskStartUrlsAsync(string actorTaskId, string? scheduleId, string? addUrl, string? removeUrl, CancellationToken cancellationToken = default)
    { 
        try
        {
             await _apifyClient.UpdateTaskStartUrlsAsync(actorTaskId, scheduleId, addUrl, removeUrl, cancellationToken);
            _logger.LogInformation("Updated start Urls of task '{ActorTaskId}'.", actorTaskId);
        }
        catch (InvalidOperationException ex)
        {
            var contextualMessage = $"Failed to update start Urls of task '{actorTaskId}'";
            LogInvalidOperationException(contextualMessage, ex);
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to update start Urls of task '{actorTaskId}'";
            LogException(contextualMessage, ex);
        }
    }

    private void LogInvalidOperationException(string contextualMessage, InvalidOperationException ex)
    {
        _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, contextualMessage);
        throw new InvalidOperationException(contextualMessage, ex);
    }

    private void LogException(string contextualMessage, Exception ex)
    {
        
        _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, contextualMessage);
        throw new InvalidOperationException(contextualMessage, ex);
    }

    public async Task<List<string>?> GetTaskIdsByScheduleIdAsync(string scheduleId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _apifyClient.GetTaskIdsByScheduleIdAsync(scheduleId, cancellationToken);
        }
        catch (InvalidOperationException ex)
        {
            var contextualMessage = $"Failed to get taskId for schedule '{scheduleId}'";
            LogInvalidOperationException(contextualMessage, ex);
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to get taskId for schedule '{scheduleId}'";
            LogException(contextualMessage, ex);
        }
        return null;
    }

    public async Task<string> CreateGroupTaskAsync(IEnumerable<JournalScheduleInfo> journals, string taskName, CancellationToken cancellationToken = default)
    {
        try
        {
            var journalList = journals.ToList();
            var urls = journalList
                .Select(j => j.Url)
                .Where(url => !string.IsNullOrWhiteSpace(url))
                .ToList();

            if (urls.Count is 0)
            {
                var errorMessage = $"No valid URLs found in journals for task '{taskName}'";
                _logger.LogWarning("No valid URLs found in journals for task '{TaskName}'", taskName);
                throw new InvalidOperationException(errorMessage);
            }

            var taskId = await _apifyClient.CreateTaskAsync(taskName, urls, urls.Count, cancellationToken);

            _logger.LogInformation("Created Apify group task '{TaskName}' with ID '{TaskId}' for {UrlCount} URLs (filtered from {TotalJournals} journals)",
                taskName, taskId, urls.Count, journalList.Count);

            return taskId;
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify group task '{taskName}'";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }
}
