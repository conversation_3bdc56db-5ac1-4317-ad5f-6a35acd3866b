﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Moq;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.AiAnalysis.Entities.Interfaces;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Application.Services.Helpers;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using PharmaLex.VigiLit.Test.Framework.ILogger;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using Xunit;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class PreClassificationServiceTests
{
    private readonly Mock<ILogger<PreClassificationService>> _mockLogger = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IVigiLitUserContext> _mockUserContext = new();

    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<IReferenceUpdateRepository> _mockReferenceUpdateRepository = new();
    private readonly Mock<IReferenceHistoryActionRepository> _mockReferenceHistoryActionRepository = new();
    private readonly Mock<IReferenceClassificationLockRepository> _mockReferenceClassificationLockRepository = new();
    private readonly Mock<IReferenceRepository> _mockReferenceRepository = new();
    private readonly Mock<IFeatureManager> _mockFeatureManager = new();
    private readonly Mock<IAiReferencePublisher> _mockAiReferencePublisher = new();
    private readonly Mock<ISubstanceService> _mockSubstanceService = new();
    private readonly Mock<IAiSuggestionRepository> _mockAiSuggestionRepository = new();

    private readonly Mock<IDashboardService> _mockImportDashboardService = new();

    private readonly Mock<IPreClassifyModelHelper> _mockPreClassifyModelHelper = new();

    private readonly IPreClassificationService _preClassificationService;

    private const ReferenceUpdate? NullReferenceUpdate = null;
    private const ReferenceUpdateModel? NullReferenceUpdateModel = null;
    private const ReferenceClassification? NullReferenceClassification = null;

    private const int UserId = 99;
    private const int ImportId = 42;
    private const int SubstanceId = 44;
    private const int ReferenceId = 45;
    private const int ClassificationId = 46;
    private const string SubstanceName = "--Substance Name--";

    private readonly FakeSubstance _substance = new(SubstanceId, SubstanceName, "TYPE");
    private readonly SubstanceSimpleModel _substanceSimpleModel = new()
    {
        Id = SubstanceId,
        Name = SubstanceName,
    };

    public PreClassificationServiceTests()
    {
        var fakeFactory = new FakeLoggerFactory<PreClassificationService>(_mockLogger);

        _preClassificationService = new PreClassificationService(
            fakeFactory,
            _mockMapper.Object,
            _mockUserContext.Object,
            _mockReferenceClassificationLockRepository.Object,
            _mockReferenceClassificationRepository.Object,
            _mockReferenceUpdateRepository.Object,
            _mockReferenceHistoryActionRepository.Object,
            _mockReferenceRepository.Object,
            _mockImportDashboardService.Object,
            _mockPreClassifyModelHelper.Object,
            _mockFeatureManager.Object,
            _mockAiReferencePublisher.Object,
            _mockSubstanceService.Object,
            _mockAiSuggestionRepository.Object
            );

        _mockUserContext.Setup(x => x.UserId).Returns(99);
    }

    [Fact]
    public async Task PreClassifyAsync_When_no_Classifications_to_update_Then_save_is_not_called_on_repositories()
    {
        // Arrange
        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(new List<PreclassifyReferenceModel>());

        // Assert
        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Never());
        _mockReferenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Never());
        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Never());
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_are_classifications_to_update_Then_save_is_called_on_repositories()
    {
        // Arrange

        const int referenceClassificationId = 42;

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = new ReferenceClassificationWithReferenceModel()
                {
                    Id = referenceClassificationId,
                }
            }
        };

        var referenceClassification = new FakeReferenceClassification(referenceClassificationId);

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(referenceClassificationId)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert
        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
        _mockReferenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_active_classifications_to_update_Then_properties_updated_history_recorded_and_saved()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42);

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = true,
            ClassificationCategoryId = 43,
            DosageForm = "DOSAGE-FORM",
            CountryOfOccurrence = "COUNTRY OF OCCURRENCE",
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = referenceClassificationWithReferenceModel,
            }
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert

        Assert.Equal(43, referenceClassification.ClassificationCategoryId);
        Assert.Equal("DOSAGE-FORM", referenceClassification.DosageForm);
        Assert.Equal("COUNTRY OF OCCURRENCE", referenceClassification.CountryOfOccurrence);

        _mockReferenceHistoryActionRepository.Verify(x => x.Add(It.Is<ReferenceHistoryAction>(rha =>
            rha.ReferenceClassificationId == 42 &&
            rha.ReferenceHistoryActionType == ReferenceHistoryActionType.Classified &&
            rha.UserId == 99)), Times.Once);

        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());

        _mockReferenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_inactive_classification_to_update_Then_properties_updated_history_recorded_as_inactive_and_saved()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42);

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = false,
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = referenceClassificationWithReferenceModel,
            }
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert
        Assert.Equal(ReferenceState.Inactive, referenceClassification.ReferenceState);
        Assert.False(referenceClassification.IsActive);

        _mockReferenceHistoryActionRepository.Verify(x => x.Add(It.Is<ReferenceHistoryAction>(rha =>
            rha.ReferenceClassificationId == 42 &&
            rha.ReferenceHistoryActionType == ReferenceHistoryActionType.Inactive &&
            rha.UserId == 99)), Times.Once);

        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_reference_update_but_not_persisted_Then_message_is_logged()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42);

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = false,
        };

        ReferenceUpdateModel referenceUpdateModel = new ReferenceUpdateModel()
        {
            Id = 43,
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
            {
                new ()
                {
                    ReferenceClassification = referenceClassificationWithReferenceModel,
                    ReferenceUpdate = referenceUpdateModel,
                }
            };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));
        _mockReferenceUpdateRepository.Setup(x => x.GetById(43)).Returns(Task.FromResult(NullReferenceUpdate));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert
        _mockLogger.VerifyLogging("PreClassifyAsync could not TakeUpdates because update is null. classificationId=42, updateId=43", LogLevel.Warning);
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_reference_update_Then_update_is_removed_from_repository()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42)
        {
            Reference = new Reference(),
        };

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = false,
        };

        ReferenceUpdateModel referenceUpdateModel = new ReferenceUpdateModel()
        {
            Id = 43,
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
            {
                new ()
                {
                    ReferenceClassification = referenceClassificationWithReferenceModel,
                    ReferenceUpdate = referenceUpdateModel,
                }
            };

        ReferenceUpdate referenceUpdate = new ReferenceUpdate();

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));
        _mockReferenceUpdateRepository.Setup(x => x.GetById(43)).Returns(Task.FromResult((ReferenceUpdate?)referenceUpdate));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert
        _mockReferenceUpdateRepository.Verify(x => x.Remove(referenceUpdate), Times.Once);
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_active_classification_to_update_with_reference_update_Then_properties_updated_history_recorded_and_saved()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42);

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = true,
            ClassificationCategoryId = 43,
            DosageForm = "DOSAGE-FORM",
            CountryOfOccurrence = "COUNTRY OF OCCURRENCE",
        };

        ReferenceUpdateModel referenceUpdateModel = new ReferenceUpdateModel()
        {
            Id = 43,
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
            {
                new ()
                {
                    ReferenceClassification = referenceClassificationWithReferenceModel,
                    ReferenceUpdate = referenceUpdateModel
                }
            };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert

        Assert.Equal(43, referenceClassification.ClassificationCategoryId);
        Assert.Equal("DOSAGE-FORM", referenceClassification.DosageForm);
        Assert.Equal("COUNTRY OF OCCURRENCE", referenceClassification.CountryOfOccurrence);

        _mockReferenceHistoryActionRepository.Verify(x => x.Add(It.Is<ReferenceHistoryAction>(rha =>
            rha.ReferenceClassificationId == 42 &&
            rha.ReferenceHistoryActionType == ReferenceHistoryActionType.Classified &&
            rha.UserId == 99)), Times.Once);

        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());

        _mockReferenceUpdateRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_inactive_classification_to_update_with_reference_update_Then_properties_updated_history_recorded_as_inactive_and_saved()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42);

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = false,
        };

        ReferenceUpdateModel referenceUpdateModel = new ReferenceUpdateModel()
        {
            Id = 43,
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
            {
                new ()
                {
                    ReferenceClassification = referenceClassificationWithReferenceModel,
                    ReferenceUpdate = referenceUpdateModel
                }
            };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert
        Assert.Equal(ReferenceState.Inactive, referenceClassification.ReferenceState);
        Assert.False(referenceClassification.IsActive);

        _mockReferenceHistoryActionRepository.Verify(x => x.Add(It.Is<ReferenceHistoryAction>(rha =>
            rha.ReferenceClassificationId == 42 &&
            rha.ReferenceHistoryActionType == ReferenceHistoryActionType.Inactive &&
            rha.UserId == 99)), Times.Once);

        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_active_classification_to_update_with_reference_update__and_classification_category_has_changed_Then_history_records_as_unchanged()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42)
        {
            ClassificationCategoryId = 44,
        };

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = true,
            ClassificationCategoryId = 44,
        };

        ReferenceUpdateModel referenceUpdateModel = new ReferenceUpdateModel()
        {
            Id = 43,
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
            {
                new ()
                {
                    ReferenceClassification = referenceClassificationWithReferenceModel,
                    ReferenceUpdate = referenceUpdateModel
                }
            };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert

        _mockReferenceHistoryActionRepository.Verify(x => x.Add(It.Is<ReferenceHistoryAction>(rha =>
            rha.ReferenceClassificationId == 42 &&
            rha.ReferenceHistoryActionType == ReferenceHistoryActionType.Unchanged &&
            rha.UserId == 99)), Times.Once);
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_active_classification_to_update_with_reference_update_and_classification_category_has_not_changed_and_dosage_has_changed_Then_history_records_as_unchanged()
    {
        // Arrange
        var referenceClassification = new FakeReferenceClassification(42)
        {
            ClassificationCategoryId = 44,
            DosageForm = "--Original Dosage--",
        };

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = true,
            ClassificationCategoryId = 44,
            DosageForm = "--Updated Dosage--",
        };

        ReferenceUpdateModel referenceUpdateModel = new ReferenceUpdateModel()
        {
            Id = 43,
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = referenceClassificationWithReferenceModel,
                ReferenceUpdate = referenceUpdateModel
            }
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert
        _mockReferenceHistoryActionRepository.Verify(x => x.Add(It.Is<ReferenceHistoryAction>(rha =>
            rha.ReferenceClassificationId == 42 &&
            rha.ReferenceHistoryActionType == ReferenceHistoryActionType.Unchanged &&
            rha.UserId == 99)), Times.Once);
    }

    [Fact]
    public async Task PickNextToPreClassify_When_there_is_are_locks_for_the_user_Then_warning_is_logged()
    {
        // Arrange

        var referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Reference = new ReferenceDetailedModel(),
        };

        var classificationIds = new List<int> { ClassificationId };
        _mockReferenceClassificationLockRepository.Setup(x => x.GetAllLockedClassificationIdsForPick()).Returns(Task.FromResult(classificationIds));
        _mockMapper.Setup(x => x.Map<ReferenceClassificationWithReferenceModel>(It.IsAny<ReferenceClassification>()))
                   .Returns(referenceClassificationWithReferenceModel);
        _mockReferenceUpdateRepository.Setup(x => x.GetForClassification(It.IsAny<int>(), It.IsAny<int>()))
            .Returns(Task.FromResult(NullReferenceUpdateModel));

        var lockingResult = new LockingResult
        {
            ClassificationIds = new List<int> { 42, 43 },
            IsSuccessful = true,
        };
        _mockReferenceClassificationLockRepository.Setup(x => x.Unlock(99)).Returns(Task.FromResult(lockingResult));

        // Act
        await _preClassificationService.PickNextToPreClassify(ImportId);

        // Assert
        _mockLogger.VerifyLogging("PickNextToPreClassify: User 99 is picking but already holds locks. Locks released. unlockedClassificationIds=42,43", LogLevel.Warning);
    }

    [Fact]
    public async Task PickNextToPreClassify_When_the_user_fails_to_find_a_classification_to_choose_Then_warning_is_logged_and_null_is_returned()
    {
        // Arrange
        const int classificationId = 43;

        var classificationIds = new List<int> { classificationId };
        _mockReferenceClassificationLockRepository.Setup(x => x.GetAllLockedClassificationIdsForPick()).Returns(Task.FromResult(classificationIds));

        SetupUnlock();

        // Act
        var result = await _preClassificationService.PickNextToPreClassify(ImportId);

        // Assert
        _mockLogger.VerifyLogging("User 99 failed to find any classifications to pick.", LogLevel.Warning);
        Assert.Null(result);
    }

    /// <remarks>
    /// This test should really be an integration test as the logic its testing is more in the repo. Keeping here as a reminder of the test case.
    /// </remarks>
    [Fact]
    public async Task PickNextToPreClassify_When_the_user_has_a_substance_preference_Then_preference_is_returned()
    {
        // Arrange
        SetupSimplePickNextToPreClassify();
        SetupLock();
        SetupUnlock();

        ReferenceClassification? referenceClassificationSubstancePreference = new ReferenceClassification(ReferenceId, SubstanceId)
        {
            Substance = _substance,
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationToPreClassify_BySubstancePreference(UserId, ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult((ReferenceClassification?)referenceClassificationSubstancePreference));

        // Act
        var result = (await _preClassificationService.PickNextToPreClassify(ImportId)).ToList();

        // Assert
        Assert.NotEmpty(result);
        var referenceClassificationResult = result.First().ReferenceClassification;
        //Assert.Equal(referenceClassificationWithReferenceModel, referenceClassificationResult);
        Assert.Equal(SubstanceId, referenceClassificationResult.Substance.Id);
        Assert.Equal(SubstanceName, referenceClassificationResult.Substance.Name);
    }

    /// <remarks>
    /// This test should really be an integration test as the logic its testing is more in the repo. Keeping here as a reminder of the test case.
    /// </remarks>
    [Fact]
    public async Task PickNextToPreClassify_When_the_user_has_no_substance_preference_Then_default_preference_is_returned()
    {
        // Arrange
        SetupSimplePickNextToPreClassify();
        SetupLock();
        SetupUnlock();

        ReferenceClassification defaultPreference = new ReferenceClassification(ReferenceId, SubstanceId)
        {
            Substance = _substance,
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationToPreClassify_BySubstancePreference(UserId, ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult(NullReferenceClassification));

        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationToPreClassify(ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult((ReferenceClassification?)defaultPreference));

        // Act
        var result = await _preClassificationService.PickNextToPreClassify(ImportId);

        // Assert
        Assert.NotEmpty(result);
        var referenceClassificationResult = result.First().ReferenceClassification;
        //Assert.Equal(referenceClassificationWithReferenceModel, referenceClassificationResult);
        Assert.Equal(ClassificationId, referenceClassificationResult.Id);
        Assert.Equal(SubstanceId, referenceClassificationResult.Substance.Id);
        Assert.Equal(SubstanceName, referenceClassificationResult.Substance.Name);
    }

    private void SetupSimplePickNextToPreClassify()
    {
        var referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = ClassificationId,
            Reference = new ReferenceDetailedModel(),
            Substance = _substanceSimpleModel,
        };

        _mockMapper.Setup(x => x.Map<ReferenceClassificationWithReferenceModel>(It.IsAny<ReferenceClassification>()))
            .Returns(referenceClassificationWithReferenceModel);

        var classificationIds = new List<int> { ClassificationId };
        _mockReferenceClassificationLockRepository.Setup(x => x.GetAllLockedClassificationIdsForPick()).Returns(Task.FromResult(classificationIds));
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_active_classification_to_update_without_reference_update__and_classification_category_has_changed_Then_duplicates_are_checked_using_the_Reference_Doi()
    {
        // Arrange
        var substance = new Substance("NAME", "TYPE");
        var substanceSimpleModel = new SubstanceSimpleModel();

        var referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Reference = new ReferenceDetailedModel()
            {
                Doi = "model.ReferenceClassification.Reference.Doi",
            },
            Substance = substanceSimpleModel,
        };

        var classificationIds = new List<int> { ClassificationId };
        _mockReferenceClassificationLockRepository.Setup(x => x.GetAllLockedClassificationIdsForPick()).Returns(Task.FromResult(classificationIds));

        var defaultPreference = new ReferenceClassification(ReferenceId, SubstanceId)
        {
            Substance = substance,
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationToPreClassify_BySubstancePreference(UserId, ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult(NullReferenceClassification));

        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationToPreClassify(ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult((ReferenceClassification?)defaultPreference));

        _mockMapper.Setup(x => x.Map<ReferenceClassificationWithReferenceModel>(It.IsAny<ReferenceClassification>()))
                   .Returns(referenceClassificationWithReferenceModel);

        var lockingResult = new LockingResult()
        {
            ClassificationIds = new List<int>(),
            IsSuccessful = true,
        };
        _mockReferenceClassificationLockRepository.Setup(x => x.Lock(It.IsAny<List<int>>(), It.IsAny<int>()))
            .Returns(Task.FromResult(lockingResult)); // NOTE This method can *only* return true. It exceptions in other cases.

        SetupUnlock();

        IEnumerable<ReferenceClassification> emptyClassificationList = new List<ReferenceClassification>();
        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationsForDuplicates(It.IsAny<int>(), "", It.IsAny<int>(), It.IsAny<List<int>>()))
            .Returns(Task.FromResult(emptyClassificationList));


        // Act
        await _preClassificationService.PickNextToPreClassify(ImportId);

        // Assert
        _mockReferenceClassificationRepository.Verify(x => x.GetReferenceClassificationsForDuplicates(It.IsAny<int>(), "model.ReferenceClassification.Reference.Doi", It.IsAny<int>(), It.IsAny<List<int>>()));
    }

    [Fact]
    public async Task PreClassifyAsync_When_there_is_active_classification_to_update_with_reference_update_and_classification_category_has_changed_Then_duplicates_are_checked_using_the_update_Doi()
    {
        // Arrange
        var referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Reference = new ReferenceDetailedModel()
            {
                Doi = "model.ReferenceClassification.Reference.Doi",
            },
            Substance = _substanceSimpleModel,
        };

        var classificationIds = new List<int> { ClassificationId };
        _mockReferenceClassificationLockRepository.Setup(x => x.GetAllLockedClassificationIdsForPick()).Returns(Task.FromResult(classificationIds));

        var defaultPreference = new ReferenceClassification(ReferenceId, SubstanceId)
        {
            Substance = _substance,
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationToPreClassify_BySubstancePreference(UserId, ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult(NullReferenceClassification));

        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationToPreClassify(ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult((ReferenceClassification?)defaultPreference));

        _mockMapper.Setup(x => x.Map<ReferenceClassificationWithReferenceModel>(It.IsAny<ReferenceClassification>()))
                   .Returns(referenceClassificationWithReferenceModel);

        var lockingResult = new LockingResult()
        {
            ClassificationIds = new List<int>(),
            IsSuccessful = true,
        };
        _mockReferenceClassificationLockRepository.Setup(x => x.Lock(It.IsAny<List<int>>(), It.IsAny<int>()))
            .Returns(Task.FromResult(lockingResult)); // NOTE This method can *only* return true. It exceptions in other cases.

        SetupUnlock();

        ReferenceUpdateModel referenceUpdateModel = new ReferenceUpdateModel()
        {
            Id = 43,
            Doi = "model.ReferenceUpdate.Doi",
        };
        _mockReferenceUpdateRepository.Setup(x => x.GetForClassification(ReferenceId, SubstanceId)).Returns(Task.FromResult((ReferenceUpdateModel?)referenceUpdateModel));

        IEnumerable<ReferenceClassification> emptyClassificationList = new List<ReferenceClassification>();
        _mockReferenceClassificationRepository.Setup(x => x.GetReferenceClassificationsForDuplicates(It.IsAny<int>(), "", It.IsAny<int>(), It.IsAny<List<int>>()))
            .Returns(Task.FromResult(emptyClassificationList));


        // Act
        await _preClassificationService.PickNextToPreClassify(ImportId);

        // Assert
        _mockReferenceClassificationRepository.Verify(x => x.GetReferenceClassificationsForDuplicates(It.IsAny<int>(), "model.ReferenceUpdate.Doi", It.IsAny<int>(), It.IsAny<List<int>>()));
    }

    /// <summary>
    /// NOTE: Needs to be removed later
    /// </summary>
    private void SetupUnlock()
    {
        var lockingResult = new LockingResult
        {
            ClassificationIds = new List<int>(),
            IsSuccessful = true,
        };
        _mockReferenceClassificationLockRepository.Setup(x => x.Unlock(99)).Returns(Task.FromResult(lockingResult));
    }

    private void SetupLock()
    {
        var lockingResult = new LockingResult()
        {
            ClassificationIds = new List<int>(),
            IsSuccessful = true,
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.Lock(It.IsAny<List<int>>(), It.IsAny<int>()))
            .Returns(Task.FromResult(lockingResult)); // NOTE This method can *only* return true. It exceptions in other cases.
    }

    [Fact]
    public async Task UserHasAllLocks_When_user_has_a_lock_on_a_classification_that_is_being_passed_in_Then_true_is_returned()
    {
        // Arrange
        const int referenceClassificationId = 45;

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId,
                },
            },
        };

        IEnumerable<ReferenceClassificationLock> referenceClassificationLocks = new List<ReferenceClassificationLock>()
        {
            new ()
            {
                ReferenceClassificationId = referenceClassificationId
            },
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.GetLocksForUser(UserId))
            .Returns(Task.FromResult(referenceClassificationLocks));

        // Act
        var result = await _preClassificationService.UserHasAllLocks(updatedClassifications);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task UserHasAllLocks_When_user_has_locks_on_classifications_that_are_being_passed_in_Then_true_is_returned()
    {
        // Arrange
        const int referenceClassificationId1 = 45;
        const int referenceClassificationId2 = 46;

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId1,
                },
            },
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId2,
                },
            },
        };

        IEnumerable<ReferenceClassificationLock> referenceClassificationLocks = new List<ReferenceClassificationLock>()
        {
            new ()
            {
                ReferenceClassificationId = referenceClassificationId1
            },
            new ()
            {
                ReferenceClassificationId = referenceClassificationId2
            },
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.GetLocksForUser(UserId))
            .Returns(Task.FromResult(referenceClassificationLocks));

        // Act
        var result = await _preClassificationService.UserHasAllLocks(updatedClassifications);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task UserHasAllLocks_When_user_does_not_have_lock_on_classification_that_is_being_passed_in_Then_false_is_returned()
    {
        // Arrange
        const int referenceClassificationId = 45;
        const int lockedReferenceClassificationId = 46;

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId,
                },
            },
        };

        IEnumerable<ReferenceClassificationLock> referenceClassificationLocks = new List<ReferenceClassificationLock>()
        {
            new ()
            {
                ReferenceClassificationId = lockedReferenceClassificationId
            },
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.GetLocksForUser(UserId))
            .Returns(Task.FromResult(referenceClassificationLocks));

        // Act
        var result = await _preClassificationService.UserHasAllLocks(updatedClassifications);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task UserHasAllLocks_When_user_has_more_locks_on_classifications_than_are_being_passed_in_and_passed_in_are_locked_Then_true_is_returned()
    {
        // Arrange
        const int referenceClassificationId1 = 45;
        const int lockedReferenceClassificationId1 = 45;
        const int lockedReferenceClassificationId2 = 46;

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId1,
                },
            },
        };

        IEnumerable<ReferenceClassificationLock> referenceClassificationLocks = new List<ReferenceClassificationLock>()
        {
            new ()
            {
                ReferenceClassificationId = lockedReferenceClassificationId1
            },
            new ()
            {
                ReferenceClassificationId = lockedReferenceClassificationId2
            },
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.GetLocksForUser(UserId))
            .Returns(Task.FromResult(referenceClassificationLocks));

        // Act
        var result = await _preClassificationService.UserHasAllLocks(updatedClassifications);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task UserHasAllLocks_When_user_does_not_have_any_locks_on_classifications_that_are_being_passed_in_Then_message_is_logged()
    {
        // Arrange
        const int referenceClassificationId1 = 45;
        const int referenceClassificationId2 = 46;

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId1,
                },
            },
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId2,
                },
            },
        };

        IEnumerable<ReferenceClassificationLock> referenceClassificationLocks = new List<ReferenceClassificationLock>();

        _mockReferenceClassificationLockRepository.Setup(x => x.GetLocksForUser(UserId))
            .Returns(Task.FromResult(referenceClassificationLocks));

        // Act
        await _preClassificationService.UserHasAllLocks(updatedClassifications);

        // Assert
        _mockLogger.VerifyLogging("UserId=99 cannot preclassify lockViolationClassificationIds=45,46 because they do not hold locks. lockedClassificationIds=none; updatedClassificationIds=45,46.", LogLevel.Warning);
    }

    [Fact]
    public async Task UserHasAllLocks_When_user_has_a_lock_on_classifications_but_not_that_which_is_being_passed_in_Then_message_is_logged()
    {
        // Arrange
        const int referenceClassificationId1 = 45;
        const int referenceClassificationId2 = 46;

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = new()
                {
                    Id = referenceClassificationId1,
                },
            },
        };

        IEnumerable<ReferenceClassificationLock> referenceClassificationLocks = new List<ReferenceClassificationLock>()
        {
            new ()
            {
                ReferenceClassificationId = referenceClassificationId2
            },
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.GetLocksForUser(UserId))
            .Returns(Task.FromResult(referenceClassificationLocks));

        // Act
        await _preClassificationService.UserHasAllLocks(updatedClassifications);

        // Assert
        _mockLogger.VerifyLogging("UserId=99 cannot preclassify lockViolationClassificationIds=45 because they do not hold locks. lockedClassificationIds=46; updatedClassificationIds=45.", LogLevel.Warning);
    }

    [Fact]
    public async Task RePreClassifyAsync_When_reference_classification_id_is_invalid_then_exception_is_thrown()
    {
        // Arrange
        var referenceClassificationModel = new ReferenceClassificationModel()
        {
            Id = 42,
        };

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult(NullReferenceClassification));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(async () =>
            await _preClassificationService.RePreClassifyAsync(referenceClassificationModel));
        Assert.Equal("Classification with id 42 was not found", exception.Message);
    }

    [Fact]
    public async Task RePreClassifyAsync_When_reference_classification_is_valid_then_classification_is_saved()
    {
        // Arrange
        var referenceClassificationModel = new ReferenceClassificationModel()
        {
            Id = 42,
        };
        ReferenceClassification referenceClassification = new ReferenceClassification(42, 1);

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        // Act 
        await _preClassificationService.RePreClassifyAsync(referenceClassificationModel);

        // Assert
        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task RePreClassifyAsync_When_reference_classification_is_valid_then_action_history_added_and_saved()
    {
        // Arrange
        var referenceClassificationModel = new ReferenceClassificationModel()
        {
            Id = 42,
        };

        ReferenceClassification referenceClassification = new FakeReferenceClassification(42);

        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        // Act 
        await _preClassificationService.RePreClassifyAsync(referenceClassificationModel);

        // Assert

        _mockReferenceHistoryActionRepository.Verify(x => x.Add(
            It.Is<ReferenceHistoryAction>(
                rha =>
                    rha.ReferenceHistoryActionType == ReferenceHistoryActionType.Classified &&
                    rha.ReferenceClassificationId == 42 &&
                    rha.UserId == 99)
        ), Times.Once);

        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task RePreClassifyAsync_When_reference_classification_is_valid_then_classification_updated()
    {
        // Arrange
        var referenceClassificationModel = new ReferenceClassificationModel()
        {
            Id = 42,
            ClassificationCategoryId = 43,
            DosageForm = "--DOSAGE--",
            CountryOfOccurrence = "UK"
        };

        ReferenceClassification referenceClassification = new FakeReferenceClassification(42);
        _mockReferenceClassificationRepository.Setup(x => x.GetByIdAsync(42)).Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        // Act 
        await _preClassificationService.RePreClassifyAsync(referenceClassificationModel);

        // Assert

        Assert.Equal(43, referenceClassification.ClassificationCategoryId);
        Assert.Equal("--DOSAGE--", referenceClassification.DosageForm);
        Assert.Equal("UK", referenceClassification.CountryOfOccurrence);
    }

    [Fact]
    public async Task Release_When_called_then_locks_released_for_user()
    {
        // Arrange
        var lockingResult = new LockingResult
        {
            ClassificationIds = new List<int>(),
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.Unlock(UserId)).Returns(Task.FromResult(lockingResult));

        // Act 
        await _preClassificationService.Release();

        // Assert
        _mockReferenceClassificationLockRepository.Verify(x => x.Unlock(UserId));
    }

    [Fact]
    public async Task Release_When_called_then_result_is_logged()
    {
        // Arrange
        var lockingResult = new LockingResult
        {
            ClassificationIds = new List<int> { 45, 46 },
        };

        _mockReferenceClassificationLockRepository.Setup(x => x.Unlock(UserId)).Returns(Task.FromResult(lockingResult));

        // Act 
        await _preClassificationService.Release();

        // Assert
        _mockLogger.VerifyLogging("Release: User 99 released locks for user 99. unlockedClassificationIds=45,46", LogLevel.Warning);
    }

    [Fact]
    public async Task PickLockedClassifications_Returns_Classification_If_Lock_Held()
    {
        // Arrange
        List<ReferenceClassificationLock> locksForUser = [new ReferenceClassificationLock() { ReferenceClassificationId = ClassificationId }];

        List<ReferenceClassification> classifications = [new FakeReferenceClassification(ReferenceId) { SubstanceId = SubstanceId }];

        ReferenceClassificationWithReferenceModel referenceClassificationWithReferenceModel = new()
        {
            Id = ClassificationId,
            Reference = new ReferenceDetailedModel(),
            Substance = _substanceSimpleModel,
        };

        _mockReferenceClassificationLockRepository
            .Setup(x => x.GetLocksForUser(It.IsAny<int>()))
            .ReturnsAsync(locksForUser);

        _mockReferenceClassificationRepository
            .Setup(x => x.GetReferenceClassificationsToPreClassify_Locked(It.IsAny<List<int>>()))
            .ReturnsAsync(classifications);

        _mockMapper.Setup(x => x.Map<ReferenceClassificationWithReferenceModel>(It.IsAny<ReferenceClassification>()))
            .Returns(referenceClassificationWithReferenceModel);

        // Act
        var models = await _preClassificationService.PickLockedClassifications();

        // Assert
        Assert.NotNull(models);
        Assert.Equal(ClassificationId, models.First().ReferenceClassification.Id);
    }

    [Fact]
    public async Task PickLockedClassifications_Returns_Null_If_No_Lock_Held()
    {
        // Arrange
        List<ReferenceClassificationLock> locksForUser = [];

        _mockReferenceClassificationLockRepository
            .Setup(x => x.GetLocksForUser(It.IsAny<int>()))
            .ReturnsAsync(locksForUser);

        // Act
        var models = await _preClassificationService.PickLockedClassifications();

        // Assert
        Assert.Null(models);
    }

    [Fact]
    public async Task PreClassifyAsync_AiEnabled_SavesAiValues()
    {
        // Arrange
        _mockFeatureManager
            .Setup(x => x.IsEnabledAsync("DisplayAiSuggestions"))
            .Returns(Task.FromResult(true));

        var referenceClassification = new FakeReferenceClassification(42);

        var referenceClassificationWithReferenceModel = new ReferenceClassificationWithReferenceModel()
        {
            Id = 42,
            IsActive = true,
            ClassificationCategoryId = 43,
            DosageForm = "DOSAGE-FORM",
            CountryOfOccurrence = "COUNTRY OF OCCURRENCE",
        };

        var aiSuggestion = new AiSuggestedClassificationModel()
        {
            Category = "cat value",
            CategoryDecision = 1,
            CategoryReason = "cat reason",
            DosageForm = "df value",
            DosageFormReason = "df reason",
            DosageFormDecision = 1
        };

        var updatedClassifications = new List<PreclassifyReferenceModel>()
        {
            new ()
            {
                ReferenceClassification = referenceClassificationWithReferenceModel,
                AiSuggestedClassification = aiSuggestion
            }
        };

        _mockReferenceClassificationRepository
            .Setup(x => x.GetByIdAsync(42))
            .Returns(Task.FromResult((ReferenceClassification?)referenceClassification));

        SetupUnlock();

        // Act
        await _preClassificationService.PreClassifyAsync(updatedClassifications);

        // Assert
        Assert.Equal("cat value", referenceClassification.AiSuggestedCategory);
        Assert.Equal(1, (int)referenceClassification.AiCategoryDecision);
        Assert.Equal("cat reason", referenceClassification.AiCategoryReason);
        Assert.Equal("df value", referenceClassification.AiSuggestedDosageForm);
        Assert.Equal(1, (int)referenceClassification.AiDosageFormDecision);
        Assert.Equal("df reason", referenceClassification.AiDosageFormReason);

        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once());
    }

    [Fact]
    public async Task PickNextToPreClassify_AiEnabled_IncludesSuggestion()
    {
        // Arrange
        _mockFeatureManager
            .Setup(x => x.IsEnabledAsync("DisplayAiSuggestions"))
            .Returns(Task.FromResult(true));

        SetupSimplePickNextToPreClassify();
        SetupLock();
        SetupUnlock();

        var defaultPreference = new ReferenceClassification(ReferenceId, SubstanceId)
        {
            Substance = _substance,
        };

        var aiSuggestion = new AiSuggestedClassificationModel()
        {
            Category = "cat value",
            CategoryDecision = 1,
            CategoryReason = "cat reason",
            DosageForm = "df value",
            DosageFormReason = "df reason",
            DosageFormDecision = 1
        };

        _mockReferenceClassificationRepository
            .Setup(x => x.GetReferenceClassificationToPreClassify_BySubstancePreference(UserId, ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult<ReferenceClassification?>(NullReferenceClassification));

        _mockReferenceClassificationRepository
            .Setup(x => x.GetReferenceClassificationToPreClassify(ImportId, It.IsAny<List<int>>()))
            .Returns(Task.FromResult<ReferenceClassification?>(defaultPreference));

        _mockReferenceClassificationRepository
            .Setup(x => x.GetAiSuggestedClassification(It.IsAny<DateTime>(), "--Substance Name--", It.IsAny<string>()))
            .Returns(Task.FromResult(aiSuggestion));

        // Act
        var result = (await _preClassificationService.PickNextToPreClassify(ImportId)).ToList();

        // Assert
        Assert.NotEmpty(result);
        var suggestionResult = result.First().AiSuggestedClassification;
        Assert.Equal("cat value", suggestionResult.Category);
        Assert.Equal(1, suggestionResult.CategoryDecision);
        Assert.Equal("cat reason", suggestionResult.CategoryReason);
        Assert.Equal("df value", suggestionResult.DosageForm);
        Assert.Equal("df reason", suggestionResult.DosageFormReason);
        Assert.Equal(1, suggestionResult.DosageFormDecision);
    }
}