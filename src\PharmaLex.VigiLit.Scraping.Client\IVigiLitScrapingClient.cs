using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Client;

public interface IVigiLitScrapingClient
{
    /// <summary>
    /// Sends the specified command to restore schedules
    /// </summary>
    /// <param name="command">The command.</param>
    /// <returns></returns>
    Task<RestoreSchedulesResult> Send(RestoreSchedulesCommand command);

    /// <summary>
    /// Sends the specified command to schedule a web crawl using a list of URLs
    /// </summary>
    /// <param name="command">The command.</param>
    /// <returns></returns>
    Task Send(CreateOrUpdateScheduleCommand command);

    /// <summary>
    /// Sends the specified command to synchronize journal schedules with APIFY
    /// </summary>
    /// <param name="command">The synchronization command.</param>
    /// <returns>Synchronization result</returns>
    Task<ScheduleSynchronizationResult> Send(SynchronizeSchedulesCommand command);

    /// <summary>
    /// Sends the specified command to synchronize a single journal schedule
    /// </summary>
    /// <param name="command">The journal synchronization command.</param>
    /// <returns>Journal synchronization result</returns>
    Task<JournalSynchronizationResult> Send(SynchronizeJournalScheduleCommand command);

    /// <summary>
    /// Sends the specified command to detect schedule changes
    /// </summary>
    /// <param name="command">The change detection command.</param>
    /// <returns>Change detection result</returns>
    Task<ScheduleChangeDetectionResult> Send(DetectScheduleChangesCommand command);

    /// <summary>
    /// Sends the specified command to validate for duplicate schedules
    /// </summary>
    /// <param name="command">The validation command.</param>
    /// <returns>Duplicate validation result</returns>
    Task<DuplicateValidationResult> Send(ValidateDuplicateSchedulesCommand command);

    /// <summary>
    /// Sends the specified command to cleanup orphaned schedules
    /// </summary>
    /// <param name="command">The cleanup command.</param>
    /// <returns>Cleanup result</returns>
    Task<ScheduleCleanupResult> Send(CleanupOrphanedSchedulesCommand command);
}
