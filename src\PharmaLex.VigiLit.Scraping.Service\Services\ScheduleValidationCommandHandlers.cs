using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class DetectScheduleChangesCommandHandler : IDetectScheduleChangesCommandHandler
{
    private readonly ILogger<DetectScheduleChangesCommandHandler> _logger;
    private readonly IApifyScheduleSynchronizationService _synchronizationService;

    public DetectScheduleChangesCommandHandler(
        ILogger<DetectScheduleChangesCommandHandler> logger,
        IApifyScheduleSynchronizationService synchronizationService)
    {
        _logger = logger;
        _synchronizationService = synchronizationService;
    }

    public async Task<ScheduleChangeDetectionResult> Consume(DetectScheduleChangesCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("DetectScheduleChangesCommandHandler: Detecting changes for {JournalCount} journals",
                command.Journals.Count);

            var result = await _synchronizationService.DetectScheduleChangesAsync(command.Journals, cancellationToken);

            _logger.LogInformation("DetectScheduleChangesCommandHandler: Completed. Changes found: {ChangeCount}, New schedules needed: {NewSchedules}",
                result.Changes.Count, result.NewSchedulesNeeded);

            return result;
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to detect schedule changes: {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            throw new InvalidOperationException(errorMessage, ex);
        }
    }
}

public class ValidateDuplicateSchedulesCommandHandler : IValidateDuplicateSchedulesCommandHandler
{
    private readonly ILogger<ValidateDuplicateSchedulesCommandHandler> _logger;
    private readonly IApifyScheduleSynchronizationService _synchronizationService;

    public ValidateDuplicateSchedulesCommandHandler(
        ILogger<ValidateDuplicateSchedulesCommandHandler> logger,
        IApifyScheduleSynchronizationService synchronizationService)
    {
        _logger = logger;
        _synchronizationService = synchronizationService;
    }

    public async Task<DuplicateValidationResult> Consume(ValidateDuplicateSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("ValidateDuplicateSchedulesCommandHandler: Validating for duplicate schedules");

            var result = await _synchronizationService.ValidateNoDuplicateSchedulesAsync(cancellationToken);

            if (result.HasDuplicates)
            {
                _logger.LogWarning("ValidateDuplicateSchedulesCommandHandler: Found {DuplicateGroups} duplicate groups with {TotalDuplicates} total duplicates",
                    result.Duplicates.Count, result.TotalDuplicates);
            }
            else
            {
                _logger.LogInformation("ValidateDuplicateSchedulesCommandHandler: No duplicate schedules found");
            }

            return result;
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to validate duplicate schedules: {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            throw new InvalidOperationException(errorMessage, ex);
        }
    }
}

public class CleanupOrphanedSchedulesCommandHandler : ICleanupOrphanedSchedulesCommandHandler
{
    private readonly ILogger<CleanupOrphanedSchedulesCommandHandler> _logger;
    private readonly IApifyScheduleSynchronizationService _synchronizationService;

    public CleanupOrphanedSchedulesCommandHandler(
        ILogger<CleanupOrphanedSchedulesCommandHandler> logger,
        IApifyScheduleSynchronizationService synchronizationService)
    {
        _logger = logger;
        _synchronizationService = synchronizationService;
    }

    public async Task<ScheduleCleanupResult> Consume(CleanupOrphanedSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("CleanupOrphanedSchedulesCommandHandler: Starting cleanup with {ActiveJournalCount} active journals. PerformActualDeletion: {PerformDeletion}",
                command.ActiveJournals.Count, command.PerformActualDeletion);

            var result = await _synchronizationService.CleanupOrphanedSchedulesAsync(command.ActiveJournals, cancellationToken);

            if (result.OrphanedSchedulesFound > 0)
            {
                _logger.LogInformation("CleanupOrphanedSchedulesCommandHandler: Found {OrphanedCount} orphaned schedules, marked {MarkedCount} for removal",
                    result.OrphanedSchedulesFound, result.RemovedScheduleIds.Count);
            }
            else
            {
                _logger.LogInformation("CleanupOrphanedSchedulesCommandHandler: No orphaned schedules found");
            }

            // Add warning about actual deletion if not performed
            if (result.OrphanedSchedulesFound > 0 && !command.PerformActualDeletion)
            {
                result.Messages.Add("Note: Orphaned schedules were identified but not deleted. Set PerformActualDeletion=true to enable deletion.");
            }

            return result;
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to cleanup orphaned schedules: {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            
            return new ScheduleCleanupResult
            {
                Errors = { errorMessage }
            };
        }
    }
}
