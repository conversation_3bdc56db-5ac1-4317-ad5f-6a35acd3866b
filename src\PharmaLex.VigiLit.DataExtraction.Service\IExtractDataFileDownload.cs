﻿using PharmaLex.BlobStorage.Descriptors;

namespace PharmaLex.VigiLit.DataExtraction.Service;

internal interface IExtractDataFileDownload
{
    Task<DownloadFile> DownloadImportedFile(Guid batchId, string fileName);
    Task Delete<T>(T fileDescriptor, CancellationToken cancellationToken = default);
    Task<bool> Exists<T>(T fileDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> Copy(ImportFileUploadDescriptor importFileUploadDescriptor, ImportFileDescriptor importFileDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> CopyBack(
            ImportFileDescriptor importFileDescriptor,
            ImportFileUploadDescriptor importFileUploadDescriptor,
            CancellationToken cancellationToken = default);

    /// <summary>
    /// Saves text to blob storage
    /// </summary>
    /// <param name="batchId">Batch id for constructing path name</param>
    /// <param name="fileName">File name for saving resultant blob</param>
    /// <param name="text">Text to save</param>
    Task CreateTextBlob(Guid batchId, string fileName, string text);

    /// <summary>
    /// Creates translation blobs for the specified batch and file, and retrieves the storage path.
    /// </summary>
    /// <param name="batchId">The unique identifier for the batch associated with the translation.</param>
    /// <param name="fileName">The name of the file for which the translation blobs are being created.</param>
    /// <param name="contextInfo">Additional context information required for processing the translation.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains the path to the created translation blobs in blob storage.
    /// </returns>
    Task<BlobStoragePath> CreateTranslationBlobsAndGetPath(Guid batchId, string fileName, ContextInfo contextInfo);
}