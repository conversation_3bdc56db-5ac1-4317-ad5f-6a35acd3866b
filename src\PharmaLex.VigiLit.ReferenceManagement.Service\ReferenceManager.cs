﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;

namespace PharmaLex.VigiLit.ReferenceManagement.Service;

public class ReferenceManager : IReferenceManager
{
    private readonly ILogger<ReferenceManager> _logger;
    private readonly IImportingReferenceUpdateRepository _referenceUpdateRepository;

    public ReferenceManager(ILoggerFactory loggerFactory, IImportingReferenceUpdateRepository referenceUpdateRepository)
    {
        _logger = loggerFactory.CreateLogger<ReferenceManager>();
        _referenceUpdateRepository = referenceUpdateRepository;
    }

    public void UpdateImportContractWithReference(ImportContract importContract, Reference reference)
    {
        _logger.LogInformation("New Reference: SourceId={SourceId}", reference.SourceId);

        // Create a new classification for this contract's substance using the new reference. The reference is magically created in the process.
        // Then assign the new classification to the contract.
        var referenceClassification = new ReferenceClassification(reference, importContract.Contract.SubstanceId);

        var importContractReferenceClassification = new ImportContractReferenceClassification(importContract, referenceClassification, ICRCType.New);
        importContract.ImportContractReferenceClassifications.Add(importContractReferenceClassification);

        // Add Reference History Action - New Reference
        var referenceHistoryAction = new ReferenceHistoryAction(ReferenceHistoryActionType.New, null);
        referenceClassification.ReferenceHistoryActions.Add(referenceHistoryAction);

        importContract.NewReferencesCount++;
    }

    public void AddReferenceUpdate(ImportContract importContract, ReferenceIdentifiers identifiers, Reference reference)
    {
        _logger.LogInformation("New Update: ReferenceId={ReferenceId}, SourceId={SourceId}", identifiers.ReferenceId, reference.SourceId);

        var update = new ReferenceUpdate()
        {
            ReferenceId = identifiers.ReferenceId,
            SubstanceId = importContract.Contract.SubstanceId,
            ImportContractId = importContract.Id
        };

        update.TakeUpdates(reference);
        _referenceUpdateRepository.Add(update);
        importContract.UpdatesCount++;
    }
}