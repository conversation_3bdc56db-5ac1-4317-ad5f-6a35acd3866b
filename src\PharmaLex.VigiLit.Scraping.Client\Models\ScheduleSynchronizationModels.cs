namespace PharmaLex.VigiLit.Scraping.Client.Models;

/// <summary>
/// Result of synchronizing all journal schedules with APIFY
/// </summary>
public class ScheduleSynchronizationResult
{
    public int JournalsProcessed { get; set; }
    public int SchedulesCreated { get; set; }
    public int SchedulesUpdated { get; set; }
    public int SchedulesSkipped { get; set; }
    public int TasksCreated { get; set; }
    public int TasksUpdated { get; set; }
    public int WebhooksCreated { get; set; }
    public int DuplicatesDetected { get; set; }
    public int DuplicatesResolved { get; set; }
    public List<string> Messages { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public bool HasErrors => Errors.Count > 0;
    public bool HasWarnings => Warnings.Count > 0;
    public TimeSpan Duration { get; set; }
}

/// <summary>
/// Result of synchronizing a specific journal schedule
/// </summary>
public class JournalSynchronizationResult
{
    public int JournalId { get; set; }
    public string JournalName { get; set; } = string.Empty;
    public string CronExpression { get; set; } = string.Empty;
    public SynchronizationAction ActionTaken { get; set; }
    public string? TaskId { get; set; }
    public string? ScheduleId { get; set; }
    public string? Message { get; set; }
    public string? Error { get; set; }
    public bool IsSuccess => string.IsNullOrEmpty(Error);
}

/// <summary>
/// Result of detecting changes between local and APIFY schedules
/// </summary>
public class ScheduleChangeDetectionResult
{
    public List<ScheduleChange> Changes { get; set; } = new();
    public int NewSchedulesNeeded { get; set; }
    public int SchedulesToUpdate { get; set; }
    public int OrphanedSchedules { get; set; }
    public int DuplicateSchedules { get; set; }
    public bool HasChanges => Changes.Count > 0;
}

/// <summary>
/// Represents a detected change in schedule configuration
/// </summary>
public class ScheduleChange
{
    public int JournalId { get; set; }
    public string JournalName { get; set; } = string.Empty;
    public ScheduleChangeType ChangeType { get; set; }
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Result of validating for duplicate schedules
/// </summary>
public class DuplicateValidationResult
{
    public List<DuplicateScheduleGroup> Duplicates { get; set; } = new();
    public int TotalDuplicates => Duplicates.Sum(d => d.ScheduleIds.Count - 1);
    public bool HasDuplicates => Duplicates.Count > 0;
}

/// <summary>
/// Group of duplicate schedules with the same cron expression
/// </summary>
public class DuplicateScheduleGroup
{
    public string CronExpression { get; set; } = string.Empty;
    public List<string> ScheduleIds { get; set; } = new();
    public List<string> TaskIds { get; set; } = new();
    public DateTime FirstCreated { get; set; }
    public DateTime LastCreated { get; set; }
}

/// <summary>
/// Result of cleaning up orphaned schedules
/// </summary>
public class ScheduleCleanupResult
{
    public int OrphanedSchedulesFound { get; set; }
    public int OrphanedSchedulesRemoved { get; set; }
    public int TasksRemoved { get; set; }
    public int WebhooksRemoved { get; set; }
    public List<string> RemovedScheduleIds { get; set; } = new();
    public List<string> Messages { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public bool HasErrors => Errors.Count > 0;
}

/// <summary>
/// Types of synchronization actions that can be taken
/// </summary>
public enum SynchronizationAction
{
    None,
    Created,
    Updated,
    Skipped,
    Error,
    DuplicateResolved
}

/// <summary>
/// Types of changes that can be detected in schedules
/// </summary>
public enum ScheduleChangeType
{
    CronExpressionChanged,
    UrlChanged,
    JournalEnabled,
    JournalDisabled,
    NewJournal,
    JournalRemoved,
    TaskIdMissing,
    ScheduleMissing
}
