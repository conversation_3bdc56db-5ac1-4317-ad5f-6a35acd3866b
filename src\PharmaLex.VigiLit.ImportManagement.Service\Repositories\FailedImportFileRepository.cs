﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories
{
    internal class FailedImportFileRepository : TrackingGenericRepository<FailedImportFile>, IFailedImportFileRepository
    {
        public FailedImportFileRepository(PlxDbContext context, IUserContext userContext)
    : base(context, userContext.User)
        {
        }

        public async Task<IEnumerable<FailedImportFile>> GetAll()
        {
            return await context.Set<FailedImportFile>()
                  .OrderByDescending(c => c.Id)
                  .ToListAsync();
        }


        public async Task<FailedImportFile?> GetById(int id)
        {
            return await context.Set<FailedImportFile>()
                .Where(i => i.Id == id)
                 .FirstOrDefaultAsync();
        }

        public override async Task<int> SaveChangesAsync()
        {
            return await this.context.SaveChangesAsync();
        }

    }
}
