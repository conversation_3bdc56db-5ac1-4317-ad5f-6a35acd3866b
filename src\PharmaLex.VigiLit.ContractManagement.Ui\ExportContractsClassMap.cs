﻿using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.ContractManagement.Ui;

public sealed class ExportContractsClassMap : CamelCaseClassMap<ExportContractModel>
{
    public ExportContractsClassMap()
    {
        Map(m => m.CompanyName).Name("Company Name");
        Map(m => m.ProjectName).Name("Project");
        Map(m => m.SubstanceName).Name("Substance");
        Map(m => m.ContractType).Name("Type");
        Map(m => m.ContractWeekday).Name("Scheduled Weekday");
        Map(m => m.SearchPeriod).Name("Search Period");
        Map(m => m.ContractStartDate).Name("Contract Start Date");
        Map(m => m.IsActive).Name("Active");
        Map(m => m.SearchVersion).Name("Search Version");
        Map(m => m.SearchString).Name("Search String");
    }
}