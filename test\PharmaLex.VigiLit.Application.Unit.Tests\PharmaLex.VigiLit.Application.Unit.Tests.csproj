﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
  </PropertyGroup>
  
  <PropertyGroup>
	<NoWarn>$(NoWarn);CA1859</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.TimeProvider.Testing" Version="9.7.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="PharmaLex.Authentication.B2C" Version="8.0.0.212" />
    <PackageReference Include="Shouldly" Version="4.3.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
	<ProjectReference Include="..\..\src\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
	<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application.Services\PharmaLex.VigiLit.Application.Services.csproj" />
	<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
	<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Company\PharmaLex.VigiLit.Company.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Infrastructure\PharmaLex.VigiLit.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Reporting\PharmaLex.VigiLit.Reporting.csproj" />
    <ProjectReference Include="..\..\src\PharmaLex.VigiLit.Search.Ui\PharmaLex.VigiLit.Search.Ui.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.Fakes\PharmaLex.VigiLit.Test.Fakes.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework\PharmaLex.VigiLit.Test.Framework.csproj" />
  </ItemGroup>

</Project>
