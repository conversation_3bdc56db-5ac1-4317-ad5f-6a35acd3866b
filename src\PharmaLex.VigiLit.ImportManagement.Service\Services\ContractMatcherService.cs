﻿using Microsoft.CodeAnalysis.Scripting;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

internal class ContractMatcherService : IContractMatcherService
{
    private readonly IImportingContractRepository _importingContractRepository;
    private readonly IReferenceMatcher _referenceMatcher;
    private readonly IFeatureManager _featureManager;
    private readonly ISubstanceRepository _substanceRepository;
    private readonly ILogger<ContractMatcherService> _logger;
    private readonly IAiReferencePublisher _aiReferencePublisher;
    private readonly IExpressionBuilder _expressionBuilder;
    private readonly ICSharpScriptFactory _cSharpScriptFactory;

    private const string AiMessageBusSend = "AiMessageBusSend";

    public ContractMatcherService(
        IImportingContractRepository importingContractRepository,
        IReferenceMatcher referenceMatcher,
        IFeatureManager featureManager,
        ISubstanceRepository substanceRepository,
        ILogger<ContractMatcherService> logger,
        IAiReferencePublisher aiReferencePublisher,
        IExpressionBuilder expressionBuilder,
        ICSharpScriptFactory cSharpScriptFactory)
    {
        _importingContractRepository = importingContractRepository;
        _referenceMatcher = referenceMatcher;
        _substanceRepository = substanceRepository;
        _logger = logger;
        _featureManager = featureManager;
        _aiReferencePublisher = aiReferencePublisher;
        _expressionBuilder = expressionBuilder;
        _cSharpScriptFactory = cSharpScriptFactory;
    }

    public async Task<List<ContractVersion>> FindMatchingContractVersions(MatchReference matchReference)
    {
        var activeContractVersions = await _importingContractRepository.GetContractsToMatchOn();
        var contractVersions = new List<ContractVersion>();

        foreach (var contractVersion in activeContractVersions)
        {
            var journalTitles = GetContractsJournalTitles(contractVersion);

            _logger.LogInformation("Raw search term {MatchCondition} to check in {AbstractText} ", contractVersion.SearchString, matchReference.AbstractText);

            var scriptAdapter = GetScriptAdapter(contractVersion.SearchString);

            if (await _referenceMatcher.Matches(matchReference, journalTitles, scriptAdapter))
            {
                contractVersions!.Add(contractVersion);
            }
        }

        return contractVersions;
    }

    private ScriptAdapter<bool> GetScriptAdapter(string searchString)
    {
        var formattedCSharpCondition = _expressionBuilder.Build(searchString);
        _logger.LogInformation("Formatted string: {FormattedString} to check in abstractText ", formattedCSharpCondition);

        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);

        var script = _cSharpScriptFactory.Create(
            formattedCSharpCondition,
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        var scriptAdapter = new ScriptAdapter<bool>(script);
        return scriptAdapter;
    }

    private static List<string> GetContractsJournalTitles(ContractVersion contractVersion)
    {
        return contractVersion.ContractVersionJournals
            .Where(x => x.Journal.Enabled)
            .Select(x => x.Journal.Name)
            .ToList();
    }

    public async Task SendToAi(Reference reference, IList<ImportContract> importContracts) // Shouldn't be here
    {
        if (await _featureManager.IsEnabledAsync(AiMessageBusSend))
        {
            foreach (var importContract in importContracts)
            {
                var substance = await _substanceRepository.Get(importContract.Contract.SubstanceId);
                var sourceSystem = Convert.ToString(reference.SourceSystem);
                var synonyms = substance.SubstanceSynonyms.Select(x => x.Name).ToList();
                var referenceData = new PreClassifyReferenceCommand(reference.Title, reference.Abstract,
                    reference.SourceId,
                    sourceSystem, substance.Name, synonyms);
                await _aiReferencePublisher.Send(referenceData);
            }
        }
    }
}