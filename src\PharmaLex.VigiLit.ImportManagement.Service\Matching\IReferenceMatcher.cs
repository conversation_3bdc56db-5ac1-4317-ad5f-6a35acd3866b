﻿namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;

internal interface IReferenceMatcher
{
    /// <summary>
    /// Matches the specified match condition against the reference.
    /// </summary>
    /// <param name="matchReference">The match operand which is a reference.</param>
    /// <param name="journalTitles">Collection of journal titles to match</param>
    /// <param name="matchingConditionScript"></param>
    /// <returns>True if the reference satisfies the condition, otherwise false</returns>
    Task<bool> Matches(MatchReference matchReference, IReadOnlyCollection<string> journalTitles, IScriptAdapter<bool> matchingConditionScript);
}