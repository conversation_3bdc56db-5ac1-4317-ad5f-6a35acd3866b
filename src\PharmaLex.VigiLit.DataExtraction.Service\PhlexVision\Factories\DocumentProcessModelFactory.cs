﻿using Microsoft.Extensions.Configuration;
using System.Text.Json;
using static PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.PhlexVisionConstants;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.Factories;

internal class DocumentProcessModelFactory : IDocumentProcessModelFactory
{
    private readonly IConfiguration _configuration;

    public DocumentProcessModelFactory(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public string GetDocumentProcessModel(string token, ExtractRequest extractRequest)
    {
        var baseUrl = _configuration[PhlexVisionCallbackBaseUrl];

        var documentLocationUrl = $"{baseUrl}DownloadDocument/{extractRequest.BatchId}/{extractRequest.FileName}";

        var result = JsonSerializer.Serialize(
            new VisionRequest
            {
                DocumentId = extractRequest.BatchId,
                DocumentDownloadUrl = documentLocationUrl,
                SuccessCallbackUrl = $"{baseUrl}success/{extractRequest.BatchId}",
                StatusCallbackUrl = $"{baseUrl}status/{extractRequest.BatchId}",
                ErrorCallbackUrl = $"{baseUrl}error/{extractRequest.BatchId}",
                DocumentUploadUrl = documentLocationUrl,
                BearerToken = token,
                Stages = ["MarkdownConverter", "Translate", "AiProcessor"],
                AiProcessor =
                [
                    new Stage()
                    {
                        Action = "metadataExtraction",
                        ConfigName = "MetadataExtraction-gpt-4o",
                        Prompt = "",
                    },
                    new Stage()
                    {
                        Action = "caseExtraction",
                        ConfigName = "CaseExtraction-gpt-4o",
                        Prompt = "",
                    }
                ],
                Priority = "urgent",
                FileExtension = "pdf",
            });

        return result;
    }
}