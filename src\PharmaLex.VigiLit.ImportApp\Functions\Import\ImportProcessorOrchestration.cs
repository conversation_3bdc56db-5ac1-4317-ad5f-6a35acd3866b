using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.DurableTask;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.ImportManagement;
using PharmaLex.VigiLit.ImportManagement.Contracts;
using System.Net;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.ImportApp.Functions.Import;

public class ImportProcessorOrchestration
{
    private readonly IImportProcessingService _importProcessingService;
    private readonly IWebsiteUriProvider _websiteUriProvider;
    private readonly IConfiguration _configuration;

    public ImportProcessorOrchestration(IImportProcessingService importProcessingService,
        IWebsiteUriProvider websiteUriProvider, IConfiguration configuration)
    {
        _importProcessingService = importProcessingService;
        _websiteUriProvider = websiteUriProvider;
        _configuration = configuration;
    }

    // This function uses a timer trigger to start the eternal orchestration.
    // The eternal orchestration must be provided an ID so we can detect if it's already running. (otherwise it gets a random ID)
    [Transaction]
    [Function(nameof(Import_StartEternalOrchestration))]
    public async Task Import_StartEternalOrchestration([TimerTrigger("0 */5 * * * *")] TimerInfo timer,
        [DurableClient] DurableTaskClient durableContext, FunctionContext executionContext)
    {
        await ImportOrchestration(durableContext, executionContext);
    }

    // This function uses a Http trigger to start the eternal orchestration.
    [Transaction]
    [Function(nameof(Import_StartEternalOrchestration_HttpTrigger))]
    public async Task<HttpResponseData> Import_StartEternalOrchestration_HttpTrigger(
        [HttpTrigger(AuthorizationLevel.Function, "get", "post")]
        HttpRequestData request, [DurableClient] DurableTaskClient durableContext, FunctionContext executionContext)
    {
        var hostName = _websiteUriProvider.Provide().ToString();
        var hostConfiguration = _configuration.GetValue<string>("HostUri");
        if (hostName == hostConfiguration)
        {
            await ImportOrchestration(durableContext, executionContext);
            return request.CreateResponse(HttpStatusCode.OK);
        }
        else
        {
            return request.CreateResponse(HttpStatusCode.Forbidden);
        }
    }

    private async Task ImportOrchestration(DurableTaskClient durableContext, FunctionContext executionContext)
    {
        ILogger logger = executionContext.GetLogger(nameof(ImportOrchestration));
        logger.LogInformation("Import logic invoked from the {Function}", executionContext.FunctionDefinition.Name);

        string instanceId = nameof(Import_EternalOrchestration);

        // Check if an instance with the specified ID already exists or an existing one stopped running(completed/failed/terminated).
        var existingInstance = await GetExistingInstance(durableContext, instanceId);
        if (existingInstance == null
            || existingInstance.RuntimeStatus == OrchestrationRuntimeStatus.Completed
            || existingInstance.RuntimeStatus == OrchestrationRuntimeStatus.Failed
            || existingInstance.RuntimeStatus == OrchestrationRuntimeStatus.Terminated)
        {
            // An instance with the specified ID doesn't exist or an existing one stopped running, create one.

            // get next contract id for processing
            var nextParams = await _importProcessingService.GetNextImportProcessingParams();

            // only start the orchestration if there's work to do
            if (nextParams.HasWorkToDo())
            {
                logger.LogInformation("Starting orchestration with ID = '{InstanceId}'.", instanceId);

                await durableContext.ScheduleNewOrchestrationInstanceAsync(instanceId, nextParams, new StartOrchestrationOptions(instanceId));
            }
            else
            {
                logger.LogInformation("Orchestration found no imports awaiting processing.");
            }
        }
        else
        {
            // An instance with the specified ID exists or an existing one still running, don't create one.
            logger.LogInformation("An instance with ID '{InstanceId}' already exists.", instanceId);
        }
    }

    // This function is an eternal orchestration.
    // It takes input and performs an activity which returns an output.
    // It then starts a new instance of itself using the output from the previous activity as the input.
    // This is achieved by calling ContinueAsNew. If that's not called then the orchestration ends. (the timer trigger will start a new one)
    [Transaction]
    [Function(nameof(Import_EternalOrchestration))]
    public async Task Import_EternalOrchestration([OrchestrationTrigger] TaskOrchestrationContext context)
    {
        var input = context.GetInput<ImportProcessingParams>();

        var output = await context.CallActivityAsync<ImportProcessingParams>(nameof(Import_Activity), input);

        if (output.HasWorkToDo())
        {
            context.ContinueAsNew(output);
        }
    }

    // This function is an activity (the unit of work within an orchestration).
    [Transaction]
    [Function(nameof(Import_Activity))]
    public async Task<ImportProcessingParams> Import_Activity([ActivityTrigger] ImportProcessingParams input, FunctionContext context)
    {
        return await _importProcessingService.ImportProcessingActivity(input);
    }

    // Returns an existing instance with the same ID, or null.
    private static async Task<OrchestrationMetadata> GetExistingInstance(DurableTaskClient durableContext, string instanceId)
    {
        var instance = await durableContext.GetInstancesAsync(instanceId);
        return instance;
    }
}