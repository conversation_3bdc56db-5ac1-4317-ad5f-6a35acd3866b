using Apify.SDK.Model;
using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

/// <summary>
/// Service for common schedule management operations shared between handlers
/// </summary>
public interface IScheduleManagementService
{
    /// <summary>
    /// Finds existing schedule by cron expression
    /// </summary>
    /// <param name="cronExpression">The cron expression to search for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Existing schedule if found, null otherwise</returns>
    Task<GetListOfSchedulesResponseDataItems?> FindExistingScheduleAsync(string cronExpression, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all existing schedules from APIFY
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>All schedules response</returns>
    Task<GetListOfSchedulesResponse?> GetAllSchedulesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new schedule with task and webhook for a single journal
    /// </summary>
    /// <param name="journal">Journal to create schedule for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created task ID if successful, null otherwise</returns>
    Task<string?> CreateScheduleWithTaskAsync(JournalScheduleInfo journal, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new schedule with group task and webhook for multiple journals
    /// </summary>
    /// <param name="journals">Journals to create group schedule for</param>
    /// <param name="cronExpression">Cron expression for the schedule</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created task ID if successful, null otherwise</returns>
    Task<string?> CreateScheduleWithGroupTaskAsync(IEnumerable<JournalScheduleInfo> journals, string cronExpression, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates existing schedule by creating new task or updating existing task URLs
    /// </summary>
    /// <param name="schedule">Existing schedule to update</param>
    /// <param name="newUrl">New URL to add</param>
    /// <param name="oldUrl">Old URL to remove (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task ID (existing or newly created)</returns>
    Task<string?> UpdateScheduleTaskAsync(GetListOfSchedulesResponseDataItems schedule, string newUrl, string? oldUrl = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates existing schedule with group task for multiple journals
    /// </summary>
    /// <param name="schedule">Existing schedule to update</param>
    /// <param name="journals">Journals to include in the group task</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task ID (existing or newly created)</returns>
    Task<string?> UpdateScheduleWithGroupTaskAsync(GetListOfSchedulesResponseDataItems schedule, IEnumerable<JournalScheduleInfo> journals, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a task for a single journal
    /// </summary>
    /// <param name="journal">Journal to create task for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created task ID if successful, null otherwise</returns>
    Task<string?> CreateTaskForJournalAsync(JournalScheduleInfo journal, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates webhook for a task if webhook URL is configured
    /// </summary>
    /// <param name="taskId">Task ID to create webhook for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if webhook was created or skipped (no URL), false if creation failed</returns>
    Task<bool> CreateWebhookIfConfiguredAsync(string taskId, CancellationToken cancellationToken = default);
}
