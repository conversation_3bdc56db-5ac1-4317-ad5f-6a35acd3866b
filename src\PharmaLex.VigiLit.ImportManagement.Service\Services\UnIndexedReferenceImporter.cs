﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

#pragma warning disable S107

internal class UnIndexedReferenceImporter : IUnIndexedReferenceImporter
{
    private readonly IImportingImportRepository _importRepository;
    private readonly IImportingImportContractRepository _importContractRepository;
    private readonly IImportReferenceRepository _importReferenceRepository;
    private readonly IReferenceRepository _referenceRepository;
    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly IReferenceHistoryActionRepository _referenceHistoryActionRepository;
    private readonly IImportingImportContractReferenceClassificationRepository _importContractReferenceClassificationRepository;
    private readonly IContractMatcherService _contractMatcherService;
    private readonly IMapper _mapper;
    private readonly TimeProvider _timeProvider;
    private readonly IAuditHandler _auditHandler;
    private readonly ILogger<UnIndexedReferenceImporter> _logger;
    private readonly IDocumentLocationService _documentLocationService;

    public UnIndexedReferenceImporter(IImportingImportRepository importRepository,
        IImportingImportContractRepository importContractRepository,
        IImportReferenceRepository importReferenceRepository,
        IReferenceRepository referenceRepository,
        IReferenceClassificationRepository referenceClassificationRepository,
        IReferenceHistoryActionRepository referenceHistoryActionRepository,
        IImportingImportContractReferenceClassificationRepository importContractReferenceClassificationRepository,
        IContractMatcherService contractMatcherService,
        IMapper mapper,
        TimeProvider timeProvider,
        IAuditHandler auditHandler,
        ILogger<UnIndexedReferenceImporter> logger,
        IDocumentLocationService documentLocationService)
    {
        _importRepository = importRepository;
        _importContractRepository = importContractRepository;
        _importReferenceRepository = importReferenceRepository;
        _referenceRepository = referenceRepository;
        _referenceClassificationRepository = referenceClassificationRepository;
        _referenceHistoryActionRepository = referenceHistoryActionRepository;
        _importContractReferenceClassificationRepository = importContractReferenceClassificationRepository;
        _contractMatcherService = contractMatcherService;
        _mapper = mapper;
        _timeProvider = timeProvider;
        _auditHandler = auditHandler;
        _logger = logger;
        _documentLocationService = documentLocationService;
    }

    private const string UntranslatedFileJson = "UntranslatedFileName";
    private const string TranslatedFileJson = "TranslatedFileName";

    public async Task ProcessEnqueuedImports()
    {
        var importReference = await _importReferenceRepository.GetNextQueued();

        if (importReference != null)
        {
            await SendStatusChangedEvent((SourceSystem)importReference.SourceSystem, importReference.CorrelationId, "Matching");

            var matchReference = await GetMatchReference(importReference);

            var matchingContracts = await _contractMatcherService.FindMatchingContractVersions(matchReference);
            if (matchingContracts.Count > 0)
            {
                var reference = _mapper.Map<Reference>(importReference);
                await SaveImportAndReferenceClassification(reference, matchingContracts, importReference.CorrelationId);
            }
            else
            {
                await SendStatusChangedEvent((SourceSystem)importReference.SourceSystem, importReference.CorrelationId, "Completed with no matches");
            }

            importReference.StatusType = ImportReferenceStatusType.Completed;
            await _importReferenceRepository.SaveChangesAsync();
        }
    }

    private async Task<MatchReference> GetMatchReference(ImportReference importReference)
    {
        var documentLocation = importReference.DocumentLocation ?? "";

        var fullUntranslatedText = await _documentLocationService.GetTextFromDocument(documentLocation, UntranslatedFileJson);
        var fullText = await _documentLocationService.GetTextFromDocument(documentLocation, TranslatedFileJson);

        var matchReference = new MatchReference
        {
            JournalTitle = importReference.JournalTitle ?? "",
            AbstractText = importReference.Abstract ?? "",
            FullText = fullText,
            FullUntranslatedText = fullUntranslatedText,
        };
        return matchReference;
    }

    private async Task SendStatusChangedEvent(SourceSystem sourceSystem, Guid correlationId, string status)
    {
        var importType = sourceSystem switch
        {
            SourceSystem.Web => ImportType.Web,
            SourceSystem.File => ImportType.File,
            SourceSystem.ManualCorrection => ImportType.ManualCorrection,
            _ => ImportType.Manual
        };

        _logger.LogInformation("Status Changed Event raised for : {ImportType}", importType);
        var statusChangedEvent = new StatusChangedEvent(nameof(importType), correlationId,
            DateTime.UtcNow, "<EMAIL>", status);
        await _auditHandler.Consume(statusChangedEvent);
    }

    private async Task SaveImportAndReferenceClassification(Reference reference, List<ContractVersion> contractVersions, Guid correlationId)
    {
        try
        {
            var import = await CompleteImport((SourceSystem)reference.SourceSystem, correlationId);

            var importContracts = await SaveImportContracts(import, contractVersions);

            await SaveReference(reference);

            var referenceClassifications = await SaveReferenceClassifications(reference, contractVersions);

            await SaveReferenceHistoryActions(referenceClassifications);

            await SaveImportContractReferenceClassifications(referenceClassifications, importContracts, contractVersions, import);

            await SendToAi(reference, importContracts, contractVersions);
        }

        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected Error for Correlation Id: {CorrelationId}", correlationId);
        }
    }

    private async Task SaveReferenceHistoryActions(IList<ReferenceClassification> referenceClassifications)
    {
        foreach (var referenceClassification in referenceClassifications)
        {
            _referenceHistoryActionRepository.Add(new ReferenceHistoryAction
            {
                ReferenceClassification = referenceClassification,
                ReferenceHistoryActionType = ReferenceHistoryActionType.New
            });
        }

        await _referenceHistoryActionRepository.SaveChangesAsync();
    }

    private async Task SaveImportContractReferenceClassifications(IList<ReferenceClassification> referenceClassifications,
                                                                    IEnumerable<ImportContract> importContracts, List<ContractVersion> contractVersions, Import import)
    {
        foreach (var importContract in importContracts)
        {
            var substanceIds =
                contractVersions.Where(x => x.ContractId == importContract.ContractId)
                    .Select(x => x.Contract.SubstanceId);

            foreach (var substanceId in substanceIds)
            {
                _importContractReferenceClassificationRepository.Add(new ImportContractReferenceClassification
                {
                    ImportId = import.Id,
                    ReferenceClassificationId = referenceClassifications.Single(x => x.SubstanceId == substanceId).Id,
                    ICRCType = ICRCType.New,
                    ImportContractId = importContract.Id
                });
            }

            // Note: should be outside loop for efficiency. Unfortunately have to save one at a time due to trigger on ImportContractReferenceClassifications causing EF failure
            await _importContractReferenceClassificationRepository.SaveChangesAsync();
        }
    }

    private async Task<IList<ReferenceClassification>> SaveReferenceClassifications(Reference newReference,
        List<ContractVersion> contractVersions)
    {
        var uniqueSubstances = contractVersions.GroupBy(x => x.Contract.SubstanceId).Select(x => x.Key).ToList();

        var referenceClassifications = uniqueSubstances.Select(x => new ReferenceClassification(newReference.Id, x)).ToArray();

        foreach (var referenceClassification in referenceClassifications)
        {
            _referenceClassificationRepository.Add(referenceClassification);
        }

        await _referenceClassificationRepository.SaveChangesAsync();

        return referenceClassifications;
    }

    private async Task<IList<ImportContract>> SaveImportContracts(Import import, List<ContractVersion> contractVersions)
    {
        var importContracts = contractVersions
            .Select(cv => ImportContract.CreateCompletedImportContract(import, cv, _timeProvider)).ToArray();

        foreach (var importContract in importContracts)
        {
            _importContractRepository.Add(importContract);
        }

        await _importContractRepository.SaveChangesAsync();

        return importContracts;
    }

    private async Task SaveReference(Reference reference)
    {
        reference.SourceId = Guid.NewGuid().ToString();

        _referenceRepository.Add(reference);
        await _referenceRepository.SaveChangesAsync();
    }

    private async Task<Import> CompleteImport(SourceSystem sourceSystem, Guid correlationId)
    {
        try
        {
            await SendStatusChangedEvent(sourceSystem, correlationId, "Completed");
            var import = await _importRepository.GetByCorrelationId(correlationId);
            import.ImportDashboardStatusType = ImportDashboardStatusType.Imported;
            return import;
        }

        catch (InvalidOperationException ex)
        {
            _logger.LogError(ex, "Correlation Id: {CorrelationId} not found or multiple with same Id", correlationId);
            throw new InvalidOperationException("Correlation Id not found or multiple logs with same Id");
        }
    }

    private async Task SendToAi(Reference reference, IList<ImportContract> importContracts, List<ContractVersion> contractVersions)
    {
        foreach (var importContract in importContracts)
        {
            importContract.Contract = contractVersions.Single(x => x.ContractId == importContract.ContractId).Contract;
        }

        await _contractMatcherService.SendToAi(reference, importContracts);
    }
}

#pragma warning restore S107