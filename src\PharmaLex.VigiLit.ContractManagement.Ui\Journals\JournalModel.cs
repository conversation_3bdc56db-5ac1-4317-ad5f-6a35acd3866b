﻿
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

public class JournalModel
{
    public int? Id { get; set; }

    public string? Url { get; set; }

    [Required(ErrorMessage = "Journal Title is required.")]
    public required string Name { get; set; }

    public required int CountryId { get; set; }

    public required bool Enabled { get; set; }

    public string? Issn { get; set; }

    public required string CronExpression { get; set; }

    public required bool IsPaid { get; set; }

}
