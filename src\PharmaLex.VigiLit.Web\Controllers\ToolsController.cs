using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.Scraping.Client;
using System.Linq;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Web.Controllers;

[Authorize(Policy = Policies.SuperAdmin)]
[Route("[controller]")]
public class ToolsController : BaseController
{
    private readonly IImportQueueService _importQueueService;
    private readonly IEmailService _emailService;
    private readonly ICaseFilesEmailService _caseFilesEmailService;
    private readonly IVigiLitScrapingClient _scrapingClient;
    private readonly IJournalRepository _journalRepository;
    private readonly ILogger<ToolsController> _logger;

    public ToolsController(
        IImportQueueService importQueueService,
        IEmailService emailService,
        ICaseFilesEmailService caseFilesEmailService,
        IVigiLitScrapingClient scrapingClient,
        IJournalRepository journalRepository,
        IDataExtractionClient dataExtractionClient,
        IUserSessionService userSessionService,
        IConfiguration configuration,
        ILogger<ToolsController> logger) : base(userSessionService, configuration)
    {
        _importQueueService = importQueueService;
        _emailService = emailService;
        _caseFilesEmailService = caseFilesEmailService;
        _scrapingClient = scrapingClient;
        _journalRepository = journalRepository;
        _logger = logger;
    }

    [HttpGet]
    public IActionResult Index()
    {
        return View();
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> EnqueueScheduledImport()
    {
        await _importQueueService.EnqueueScheduledImport(ImportTriggerType.Manual);

        AddNotification("Executed: Enqueue Scheduled Import.", UserNotificationType.Confirm);

        return RedirectToAction("ImportLog", "Import");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> RestoreJournalSchedules()
    {
        var journals = await _journalRepository.GetAll();
        var enabledJournalsWithSchedules = journals
            .Where(j => j.Enabled && !string.IsNullOrEmpty(j.CronExpression))
            .ToList();

        if (enabledJournalsWithSchedules.Count is 0)
        {
            AddNotification($"No journals found that require restoration.", UserNotificationType.Warning);
        }

        var result = await _scrapingClient.Send(new RestoreSchedulesCommand()
        {
            Journals = [.. enabledJournalsWithSchedules.Select(j => new PharmaLex.VigiLit.Scraping.Client.Models.JournalScheduleInfo
            {
                Id = j.Id,
                Name = j.Name,
                Url = j.Url ?? string.Empty,
                CronExpression = j.CronExpression
            })]
        });

        if (result.Errors.Count is not 0)
        {
            _logger.LogWarning("Journal schedule restoration completed with {ErrorCount} errors. " +
                "Tasks: {Tasks}, Schedules: {Schedules}, Webhooks: {Webhooks}",
                result.Errors.Count, result.TasksCreated, result.SchedulesCreated, result.WebhooksCreated);

            AddNotification($"Journal schedule restoration completed with {result.Errors.Count} errors. " +
                $"Created - Tasks: {result.TasksCreated}, Schedules: {result.SchedulesCreated}, Webhooks: {result.WebhooksCreated}. " +
                $"Check logs for error details.", UserNotificationType.Warning);
        }
        else
        {
            _logger.LogInformation("Journal schedule restoration completed successfully. " +
                "Tasks: {Tasks}, Schedules: {Schedules}, Webhooks: {Webhooks}, Journals: {Journals}",
                result.TasksCreated, result.SchedulesCreated, result.WebhooksCreated, result.JournalsProcessed);

            AddNotification($"Journal schedule restoration completed successfully. " +
                $"Created - Tasks: {result.TasksCreated}, Schedules: {result.SchedulesCreated}, Webhooks: {result.WebhooksCreated} " +
                $"for {result.JournalsProcessed} journals.", UserNotificationType.Confirm);
        }

        return RedirectToAction("Index");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SendDailyReferenceClassificationEmails()
    {
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Manual);

        AddNotification("Executed: Send Daily Reference Classification Emails", UserNotificationType.Confirm);

        return RedirectToAction("Index", "Emails");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SendDailyCaseFilesEmails()
    {
        await _caseFilesEmailService.Send(EmailTriggerType.Manual);

        AddNotification("Executed: Send Daily Case Files Emails", UserNotificationType.Confirm);

        return RedirectToAction("Index", "Emails");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SynchronizeJournalSchedules()
    {
        var journals = await _journalRepository.GetAll();
        var enabledJournalsWithSchedules = journals
            .Where(j => j.Enabled && !string.IsNullOrEmpty(j.CronExpression))
            .ToList();

        if (enabledJournalsWithSchedules.Count is 0)
        {
            AddNotification("No journals found that require synchronization.", UserNotificationType.Warning);
            return RedirectToAction("Index");
        }

        var command = new PharmaLex.VigiLit.Scraping.Client.Models.SynchronizeSchedulesCommand
        {
            Journals = enabledJournalsWithSchedules.Select(j => new PharmaLex.VigiLit.Scraping.Client.Models.JournalScheduleInfo
            {
                Id = j.Id,
                Name = j.Name,
                Url = j.Url ?? string.Empty,
                CronExpression = j.CronExpression
            }).ToList(),
            SynchronizationType = PharmaLex.VigiLit.Scraping.Client.Models.SynchronizationType.Full,
            CleanupOrphanedSchedules = true,
            ResolveDuplicates = true
        };

        var result = await _scrapingClient.Send(command);

        if (result.HasErrors)
        {
            _logger.LogWarning("Journal schedule synchronization completed with {ErrorCount} errors. " +
                "Created: {Created}, Updated: {Updated}, Duplicates Resolved: {DuplicatesResolved}",
                result.Errors.Count, result.SchedulesCreated, result.SchedulesUpdated, result.DuplicatesResolved);

            AddNotification($"Journal schedule synchronization completed with {result.Errors.Count} errors. " +
                $"Created: {result.SchedulesCreated}, Updated: {result.SchedulesUpdated}, " +
                $"Duplicates Resolved: {result.DuplicatesResolved}. Check logs for error details.",
                UserNotificationType.Warning);
        }
        else
        {
            _logger.LogInformation("Journal schedule synchronization completed successfully. " +
                "Created: {Created}, Updated: {Updated}, Skipped: {Skipped}, Duplicates Resolved: {DuplicatesResolved}, Duration: {Duration}ms",
                result.SchedulesCreated, result.SchedulesUpdated, result.SchedulesSkipped, result.DuplicatesResolved, result.Duration.TotalMilliseconds);

            AddNotification($"Journal schedule synchronization completed successfully. " +
                $"Created: {result.SchedulesCreated}, Updated: {result.SchedulesUpdated}, " +
                $"Skipped: {result.SchedulesSkipped}, Duplicates Resolved: {result.DuplicatesResolved} " +
                $"for {result.JournalsProcessed} journals in {result.Duration.TotalSeconds:F1}s.",
                UserNotificationType.Confirm);
        }

        if (result.HasWarnings)
        {
            AddNotification($"Synchronization completed with {result.Warnings.Count} warnings. Check logs for details.",
                UserNotificationType.Warning);
        }

        return RedirectToAction("Index");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> ValidateJournalSchedules()
    {
        var journals = await _journalRepository.GetAll();
        var enabledJournalsWithSchedules = journals
            .Where(j => j.Enabled && !string.IsNullOrEmpty(j.CronExpression))
            .ToList();

        if (enabledJournalsWithSchedules.Count is 0)
        {
            AddNotification("No journals found to validate.", UserNotificationType.Warning);
            return RedirectToAction("Index");
        }

        var command = new PharmaLex.VigiLit.Scraping.Client.Models.SynchronizeSchedulesCommand
        {
            Journals = enabledJournalsWithSchedules.Select(j => new PharmaLex.VigiLit.Scraping.Client.Models.JournalScheduleInfo
            {
                Id = j.Id,
                Name = j.Name,
                Url = j.Url ?? string.Empty,
                CronExpression = j.CronExpression
            }).ToList(),
            SynchronizationType = PharmaLex.VigiLit.Scraping.Client.Models.SynchronizationType.ValidationOnly,
            ValidationOnly = true
        };

        var result = await _scrapingClient.Send(command);

        if (result.HasErrors)
        {
            _logger.LogWarning("Journal schedule validation completed with {ErrorCount} errors", result.Errors.Count);
            AddNotification($"Journal schedule validation failed with {result.Errors.Count} errors. Check logs for details.",
                UserNotificationType.Error);
        }
        else
        {
            var issuesFound = result.DuplicatesDetected > 0 || result.Messages.Any(m => m.Contains("needed") || m.Contains("required"));

            if (issuesFound)
            {
                _logger.LogInformation("Journal schedule validation found issues. Duplicates: {Duplicates}, Messages: {MessageCount}",
                    result.DuplicatesDetected, result.Messages.Count);

                AddNotification($"Journal schedule validation completed. Found {result.DuplicatesDetected} duplicates and " +
                    $"{result.Messages.Count} potential issues. Use 'Synchronize Journal Schedules' to resolve.",
                    UserNotificationType.Warning);
            }
            else
            {
                _logger.LogInformation("Journal schedule validation completed successfully with no issues found");
                AddNotification($"Journal schedule validation completed successfully. All {result.JournalsProcessed} journals are properly synchronized.",
                    UserNotificationType.Confirm);
            }
        }

        return RedirectToAction("Index");
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> CleanupOrphanedSchedules()
    {
        var journals = await _journalRepository.GetAll();
        var enabledJournalsWithSchedules = journals
            .Where(j => j.Enabled && !string.IsNullOrEmpty(j.CronExpression))
            .ToList();

        var command = new PharmaLex.VigiLit.Scraping.Client.Models.SynchronizeSchedulesCommand
        {
            Journals = enabledJournalsWithSchedules.Select(j => new PharmaLex.VigiLit.Scraping.Client.Models.JournalScheduleInfo
            {
                Id = j.Id,
                Name = j.Name,
                Url = j.Url ?? string.Empty,
                CronExpression = j.CronExpression
            }).ToList(),
            SynchronizationType = PharmaLex.VigiLit.Scraping.Client.Models.SynchronizationType.CleanupOnly
        };

        var result = await _scrapingClient.Send(command);

        if (result.HasErrors)
        {
            _logger.LogWarning("Orphaned schedule cleanup completed with {ErrorCount} errors", result.Errors.Count);
            AddNotification($"Orphaned schedule cleanup failed with {result.Errors.Count} errors. Check logs for details.",
                UserNotificationType.Error);
        }
        else
        {
            var orphanedFound = result.Messages.Any(m => m.Contains("orphaned") && !m.Contains("No orphaned"));

            if (orphanedFound)
            {
                _logger.LogInformation("Orphaned schedule cleanup found schedules to clean up");
                AddNotification("Orphaned schedule cleanup completed. Found schedules marked for manual removal. Check logs for details.",
                    UserNotificationType.Warning);
            }
            else
            {
                _logger.LogInformation("Orphaned schedule cleanup completed with no orphaned schedules found");
                AddNotification("Orphaned schedule cleanup completed successfully. No orphaned schedules found.",
                    UserNotificationType.Confirm);
            }
        }

        return RedirectToAction("Index");
    }
}