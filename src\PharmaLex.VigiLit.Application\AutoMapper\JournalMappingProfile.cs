﻿using AutoMapper;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class JournalMappingProfile : Profile
{
    public JournalMappingProfile()
    {
        CreateMap<Journal, JournalViewModel>()
            .ForMember(dest => dest.CountryName, opt => opt.MapFrom(src => src.Country.Name))
            .ForMember(dest => dest.IsPaid, opt => opt.MapFrom(src => src.SubscriptionType == JournalSubscriptionType.Paid))
            .ReverseMap()
            .ForMember(dest => dest.SubscriptionType, opt => opt.MapFrom(src => src.IsPaid ? JournalSubscriptionType.Paid : JournalSubscriptionType.OpenAccess));

        CreateMap<Journal, JournalModel>()
            .ForMember(dest => dest.IsPaid, opt => opt.MapFrom(src => src.SubscriptionType == JournalSubscriptionType.Paid))
            .ReverseMap()
            .ForMember(dest => dest.SubscriptionType, opt => opt.MapFrom(src => src.IsPaid ? JournalSubscriptionType.Paid : JournalSubscriptionType.OpenAccess));
    }

}