﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="Views\Search\Index.cshtml" />
		<None Remove="Views\Search\PrintPreview.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Views\Search\Index.cshtml" />
		<EmbeddedResource Include="Views\Search\PrintPreview.cshtml" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="CsvHelper" Version="33.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
		<PackageReference Include="Microsoft.FeatureManagement" Version="4.2.1" />
		<PackageReference Include="PharmaLex.Caching.Data" Version="8.0.0.202" />
		<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
		<ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Ui.ViewModels\PharmaLex.VigiLit.Ui.ViewModels.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="ClassMap\" />
	</ItemGroup>
</Project>