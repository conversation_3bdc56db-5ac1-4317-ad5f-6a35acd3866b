﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using SendGrid;
using System.Text.Json;

namespace PharmaLex.VigiLit.Infrastructure.Emails;

public class EmailMaintenanceService : IEmailMaintenanceService
{
    private readonly ILogger<EmailMaintenanceService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ICompanyUserRepository _companyUserRepository;
    private readonly IEmailSuppressionRepository _suppressionRepository;


    public EmailMaintenanceService(
        ILoggerFactory loggerFactory,
        IConfiguration configuration,
        ICompanyUserRepository companyUserRepository,
        IEmailSuppressionRepository suppressionRepository)
    {
        _logger = loggerFactory.CreateLogger<EmailMaintenanceService>();
        _configuration = configuration;
        _companyUserRepository = companyUserRepository;
        _suppressionRepository = suppressionRepository;
    }

    [Trace]
    public async Task UpdateSuppressions()
    {
        var bounces = await GetBounceList(null);
        var blocks = await GetBlockList(null);
        var spams = await GetSpamList(null);

        // Retrieve all unique suppressions
        var allSuppresions = bounces.Concat(blocks).Concat(spams).GroupBy(x => x.email).Select(x => x.First()).ToList();

        // Remove non-VigiLit suppressions
        var companyUsers = await _companyUserRepository.GetAllCompanyUsersWithSuppressions();
        allSuppresions.RemoveAll(x => !companyUsers.Any(y => x.email == y.User.Email));

        // map suppressions to users for saving
        var emailSuppressions = allSuppresions.Join(companyUsers,
                                    sr => sr.email,
                                    cu => cu.User.Email,
                                    (suppression, user) => new EmailSuppression
                                    {
                                        UserId = user.User.Id,
                                        EmailSuppressionType = suppression.EmailSuppressionType,
                                        Created = suppression.created,
                                        Email = suppression.email,
                                        Reason = suppression.reason,
                                        Status = suppression.status,
                                    }).ToList();

        await _suppressionRepository.DeleteAll();

        await _suppressionRepository.AddRangeAsync(emailSuppressions);
    }

    public async Task<EmailSuppressionModel> GetUserSuppression(int userId)
    {
        return await _suppressionRepository.GetUserSuppression(userId);
    }

    public async Task<IEnumerable<SendGridSuppressionResponse>> GetBlockList(DateTime? startDate)
    {
        var result = await CallSendGridSuppressionApi("suppression/blocks", startDate);
        ProcessResult(result, EmailSuppressionType.Block);
        return result;
    }

    public async Task<IEnumerable<SendGridSuppressionResponse>> GetBounceList(DateTime? startDate)
    {
        var result = await CallSendGridSuppressionApi("suppression/bounces", startDate);
        ProcessResult(result, EmailSuppressionType.Bounce);
        return result;
    }

    public async Task<IEnumerable<SendGridSuppressionResponse>> GetSpamList(DateTime? startDate)
    {
        var result = await CallSendGridSuppressionApi("suppression/spam_reports", startDate);
        ProcessResult(result, EmailSuppressionType.Spam);
        return result;
    }

    internal static void ProcessResult(IEnumerable<SendGridSuppressionResponse> results, EmailSuppressionType suppressionType)
    {
        foreach (var item in results)
        {
            item.EmailSuppressionType = suppressionType;
            item.reason = string.IsNullOrEmpty(item.reason) ? "" : item.reason;
            item.status = string.IsNullOrEmpty(item.status) ? "" : item.status;
        }
    }

    public async Task<bool> DeleteBlock(string email)
    {
        string apiKey = _configuration.GetConnectionString("Sendgrid-maintenance-API-key");

        var client = new SendGridClient(apiKey);

        var data = @"{            
            ""emails"": [
                ""EMAIL""
            ]
        }".Replace("EMAIL", email);

        var response = await client.RequestAsync(
            method: BaseClient.Method.DELETE,
            urlPath: "suppression/blocks",
            requestBody: data
        );

        return response.IsSuccessStatusCode;
    }

    public async Task<bool> DeleteBounce(string email)
    {
        string apiKey = _configuration.GetConnectionString("Sendgrid-maintenance-API-key");
        var client = new SendGridClient(apiKey);

        var response = await client.RequestAsync(
            method: BaseClient.Method.DELETE,
            urlPath: $"suppression/bounces/{email}"
        );

        return response.IsSuccessStatusCode;
    }

    private async Task<IEnumerable<SendGridSuppressionResponse>> CallSendGridSuppressionApi(string url, DateTime? startDate = null)
    {
        var results = new List<SendGridSuppressionResponse>();

        var recordStart = 0;
        var recordLimit = 500;
        var recordRemaining = true;
        string apiKey = _configuration.GetConnectionString("Sendgrid-maintenance-API-key");

        var headers = new Dictionary<string, string>()
        {
            { "Accept", "application/json"}
        };

        var client = new SendGridClient(apiKey: apiKey, requestHeaders: headers);

        while (recordRemaining)
        {
            var startTime = startDate.HasValue ? $"'start_time': {DateTimeToUnixTimestamp((DateTime)startDate)}," : "";
            var offset = $"'offset': {recordStart},";
            var limit = $"'limit':  {recordLimit}";

            var queryParams = @"{                                    
                START_TIME                
                OFFSET
                LIMIT
            }".Replace("START_TIME", startTime)
                .Replace("OFFSET", offset)
                .Replace("LIMIT", limit);

            var response = await client.RequestAsync(
                    method: BaseClient.Method.GET,
                    urlPath: url,
                    queryParams: queryParams
            );

            var responseBody = response.Body.ReadAsStringAsync().Result;

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<IList<SendGridSuppressionResponse>>(responseBody);

                if (result != null && result.Count > 0)
                {
                    results.AddRange(result);
                    recordStart += recordLimit;
                }
                else
                {
                    recordRemaining = false;
                }
            }
            else
            {
                _logger.LogWarning("SendGrid fetch {Url} failed. statusCode: {StatusCode}; responseBody: {ResponseBody}; recordStart: {RecordStart}.", url, response.StatusCode, responseBody, recordStart);
                break;
            }
        }

        return results;
    }

    private static long DateTimeToUnixTimestamp(DateTime dateTime)
    {
        return (long)(TimeZoneInfo.ConvertTimeToUtc(dateTime) -
               new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)).TotalSeconds;
    }
}
