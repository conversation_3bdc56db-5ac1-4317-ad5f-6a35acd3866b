﻿using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

namespace PharmaLex.VigiLit.ImportManagement.Ui;

public sealed class ImportDashboardDetailsRowModelClassMap : ImportManagement.Ui.CamelCaseClassMap<DashboardDetailsRowModel>
{
    public ImportDashboardDetailsRowModelClassMap()
    {
        Map(m => m.CompanyName).Name("Company");
        Map(m => m.ProjectName).Name("Project");
        Map(m => m.SubstanceName).Name("Substance");
        Map(m => m.SourceModificationDate).Name("Mod Date (ET)");
        Map(m => m.ICRCType).Name("Type");
        Map(m => m.ReferenceClassificationId).Name("PLX ID");
        Map(m => m.SourceId).Name("SourceId");
        Map(m => m.ClassificationStatus).Name("Classif. Status");
    }
}