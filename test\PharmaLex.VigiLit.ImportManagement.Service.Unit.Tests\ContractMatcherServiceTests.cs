﻿using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Moq;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.ImportManagement.Service.Services;
using PharmaLex.VigiLit.Test.Fakes.Entities;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class ContractMatcherServiceTests
{
    private readonly IContractMatcherService _contractMatcherService;
    private readonly Mock<IImportingContractRepository> _importingContractRepository = new();
    private readonly Mock<IReferenceMatcher> _referenceMatcher = new();
    private readonly Mock<IFeatureManager> _featureManager = new();
    private readonly Mock<ISubstanceRepository> _substanceRepository = new();
    private readonly Mock<ILogger<ContractMatcherService>> _mockLogger = new();
    private readonly Mock<IAiReferencePublisher> _aiReferencePublisher = new();
    private readonly Mock<IExpressionBuilder> _expressionBuilder = new();
    private readonly Mock<ICSharpScriptFactory> _scriptFactory = new();

    public ContractMatcherServiceTests()
    {
        _contractMatcherService = new ContractMatcherService(
            _importingContractRepository.Object,
            _referenceMatcher.Object,
            _featureManager.Object,
            _substanceRepository.Object,
            _mockLogger.Object,
            _aiReferencePublisher.Object,
            _expressionBuilder.Object,
            _scriptFactory.Object);
    }

    [Fact]
    public async Task FindMatchingContractVersions_ReturnsOnlyEnabledMatchingContracts()
    {
        // Arrange
        var reference = SetMatchReference();
        var contractVersions = GetContractVersions();
        _importingContractRepository
            .Setup(repo => repo.GetContractsToMatchOn())
            .ReturnsAsync(contractVersions);

        _referenceMatcher
            .Setup(matcher => matcher.Matches(It.IsAny<MatchReference>(), It.IsAny<IReadOnlyCollection<string>>(), It.IsAny<IScriptAdapter<bool>>()))
            .ReturnsAsync((MatchReference _, List<string> journalTitles, IScriptAdapter<bool> _) =>
                journalTitles.Contains("enabled journal"));

        // Act
        var result = await _contractMatcherService.FindMatchingContractVersions(reference);

        // Assert
        Assert.Single(result);
        Assert.Equal("Copper", result.First().SearchString);

        // Ensure only the enabled journal was used in matching
        var matchedJournals = result.First().ContractVersionJournals
            .Where(j => j.Journal.Enabled)
            .Select(j => j.Journal.Name);

        Assert.Contains("enabled journal", matchedJournals);
        Assert.DoesNotContain("journal2", matchedJournals);
    }

    private static MatchReference SetMatchReference()
    {
        var matchReference = new MatchReference()
        {
            AbstractText = "Copper abuse",
            JournalTitle = "journal1",
            FullText = string.Empty,
            FullUntranslatedText = string.Empty
        };
        return matchReference;
    }


    private static List<ContractVersion> GetContractVersions()
    {
        var contractVersion1 = new FakeContractVersion(43, ContractType.Scheduled, ContractVersionStatus.Approved, new Contract(123, 345), 888)
        {
            IsActive = true,
            SearchString = "Copper",

            ContractVersionJournals = new List<ContractVersionJournal>
                {
                    new ContractVersionJournal { Journal = new Journal { Name = "enabled journal", Enabled = true } }
                }
        };

        var contractVersion2 = new FakeContractVersion(43, ContractType.Scheduled, ContractVersionStatus.Approved, new Contract(678, 999), 777)
        {
            IsActive = true,
            SearchString = "contraindication OR deaths OR dependency",
            ContractVersionJournals = new List<ContractVersionJournal>
                {
                    new ContractVersionJournal { Journal = new Journal { Name = "journal2", Enabled= false } }
                }
        };

        var activeContractVersions = new List<ContractVersion>
        {
            contractVersion1,
            contractVersion2
        };
        return activeContractVersions;
    }
}