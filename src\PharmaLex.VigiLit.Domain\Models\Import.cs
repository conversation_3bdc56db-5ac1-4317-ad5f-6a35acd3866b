﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.ImportManagement.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.Domain.Models;

public class Import : VigiLitEntityBase
{
    public ImportType ImportType { get; set; }
    public ImportTriggerType ImportTriggerType { get; set; }

    public ImportDashboardStatusType ImportDashboardStatusType { get; set; }

    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }

    public ImportStatusType ImportStatusType { get; set; }

    public DateTime? ImportDate { get; set; }

    [MaxLength(100)]
    [Column(TypeName = "varchar(100)")]
    public string Message { get; set; }

    public Guid CorrelationId { get; set; }

    public ICollection<ImportContract> ImportContracts { get; set; } = new List<ImportContract>();

    public Import()
    {
    }

    public Import(ImportType type, ImportTriggerType triggerType, DateTime? importDate)
    {
        ImportType = type;
        ImportTriggerType = triggerType;
        ImportStatusType = ImportStatusType.Queued;
        ImportDate = importDate;
    }

    public void StartProcessing()
    {
        if (ImportStatusType == ImportStatusType.Queued)
        {
            StartDate = DateTime.UtcNow;
            ImportStatusType = ImportStatusType.Started;
        }
    }

    public void EndProcessing(ImportStatusType status)
    {
        // don't overwrite during a retry
        if (!EndDate.HasValue)
        {
            EndDate = DateTime.UtcNow;
        }

        ImportStatusType = status;
        ImportDashboardStatusType = ImportDashboardStatusType.Imported;
    }
    public bool HasNoImportContracts()
    {
        return ImportContracts.Count == 0;
    }
}
