﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using System.Reflection;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class MandatoryFieldsConfidenceChecker : IExtractionValidator
{
    private readonly ILogger<MandatoryFieldsConfidenceChecker> _logger;
    private readonly List<PropertyInfo> _mandatoryFields = [];
    private readonly float _mandatoryFieldMinimumConfidenceLevel;
    public MandatoryFieldsConfidenceChecker(ILogger<MandatoryFieldsConfidenceChecker> logger, IConfiguration configuration)
    {
        PopulatePropertyInfoCollections();
        _logger = logger;
        _mandatoryFieldMinimumConfidenceLevel = configuration.GetValue<float>("DataExtraction:MandatoryFieldMinimumConfidenceLevel");
    }

    public bool IsValid(ExtractedReference extractedReference)
    {
        return DoFieldsPassConfidenceLevel(extractedReference, _mandatoryFields, _mandatoryFieldMinimumConfidenceLevel);
    }

    private bool DoFieldsPassConfidenceLevel(ExtractedReference reference, List<PropertyInfo> fields, float confidenceLevel)
    {
        foreach (var field in fields)
        {
            if (field.GetValue(reference) is ICollection<IExtractedProperty> items)
            {
                if (items.AnyFailConfidence(confidenceLevel))
                {
                    _logger.LogInformation("Property: {FieldName} has low confidence.", field.Name);
                    return false;
                }
            }
            else
            {
                var extractedProperty = field.GetValue(reference) as IExtractedProperty;
                if (extractedProperty is null || extractedProperty.FailConfidence(confidenceLevel))
                {
                    _logger.LogInformation("Property: {FieldName} has low confidence.", field.Name);
                    return false;
                }
            }
        }

        return true;
    }

    private void PopulatePropertyInfoCollections()
    {
        var properties = typeof(ExtractedReference).GetProperties();

        _mandatoryFields.AddRange(
            properties.Where(info => Attribute.IsDefined(info, typeof(MandatoryAttribute)))
        );
    }
}