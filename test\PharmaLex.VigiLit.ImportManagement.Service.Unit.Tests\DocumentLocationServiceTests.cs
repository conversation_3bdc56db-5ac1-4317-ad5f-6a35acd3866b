﻿using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Service.Extensions;
using PharmaLex.VigiLit.ImportManagement.Documents;
using PharmaLex.VigiLit.ImportManagement.Service.Services;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class DocumentLocationServiceTests
{
    private readonly IDocumentLocationService _documentLocationService;
    private readonly Mock<IImportFileDocumentService> _mockIImportFileDocumentService = new();
    private readonly Mock<ILogger<DocumentLocationService>> _mockLogger = new();

    public DocumentLocationServiceTests()
    {
        _documentLocationService = new DocumentLocationService(_mockIImportFileDocumentService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GetTextFromDocument_Successfuly_Retrieves_Text_From_Blob_Storage()
    {
        // Arrange       
        var blobText = "Some text";
        var streamData = blobText.ToStream();
        var searchElement = "UntranslatedFileName";
        var documentLocation = "{\"Container\":\"x\", \"BatchId\":\"26ef2e60-7acc-41e7-8f94-50cceb20788a\", \"UntranslatedFileName\": \"somefile.txt\"}";

        _mockIImportFileDocumentService.Setup(x => x.OpenRead(It.IsAny<ImportFileDescriptor>(), It.IsAny<CancellationToken>())).ReturnsAsync(streamData);

        // Act
        var result = await _documentLocationService.GetTextFromDocument(documentLocation, searchElement);

        // Assert
        Assert.Equal(blobText, result);
    }

    [Theory]    
    [InlineData("badly formed json", "UntranslatedFileName")]                                                                                                       // bad json
    [InlineData("{\"Container\":\"x\", \"BatchId\":\"26ef2e60-7acc-41e7-8f94-50cceb20788a\", \"UntranslatedFileName\": \"somefile.txt\"}", "")]                     // missing json element to match on
    [InlineData("{\"Container\":\"x\", \"UntranslatedFileName\": \"somefile.txt\"}", "UntranslatedFileName")]                                                       // no batch id
    [InlineData("{\"Container\":\"x\", \"BatchId\":\"26ef2e60-7acc-41e7-8f94-50cceb20788a\", \"UntranslatedFileName\": \"\"}", "UntranslatedFileName")]             // empty filename
    [InlineData("{\"Container\":\"x\", \"BatchId\":\"26ef2e60-7acc-41e7-8f94-50cceb20788a\", \"UntranslatedFileName\": \"somefile.txt\"}", "wont_find_element")]    // non matching json element
    public async Task GetTextFromDocument_Returns_Empty_String_If_Location_Has_Missing_Data(string documentLocation, string searchElement )
    {
        // Arrange

        // Act
        var result = await _documentLocationService.GetTextFromDocument(documentLocation, searchElement);

        // Assert
        Assert.Equal("", result);
    }    
}
