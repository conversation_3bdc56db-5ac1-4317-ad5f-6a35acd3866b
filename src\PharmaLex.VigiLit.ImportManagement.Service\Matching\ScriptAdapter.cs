﻿using Microsoft.CodeAnalysis.Scripting;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;

internal class ScriptAdapter<T> : IScriptAdapter<T>
{
    private readonly Script<T> _script;

    public ScriptAdapter(Script<T> script)
    {
        _script = script;
    }

    public async Task<T> RunAsync(object globals, CancellationToken cancellationToken = default)
    {
        // Should really return a ScriptState<T> but this is quicker to develop just for now
        var result = await _script.RunAsync(globals, cancellationToken);
        return result.ReturnValue;
    }
}
