namespace PharmaLex.VigiLit.Scraping.Client.Models;

/// <summary>
/// Command to synchronize journal schedules with APIFY
/// </summary>
public class SynchronizeSchedulesCommand
{
    /// <summary>
    /// Journals to synchronize with APIFY
    /// </summary>
    public List<JournalScheduleInfo> Journals { get; set; } = new();

    /// <summary>
    /// Type of synchronization to perform
    /// </summary>
    public SynchronizationType SynchronizationType { get; set; } = SynchronizationType.Full;

    /// <summary>
    /// Whether to perform cleanup of orphaned schedules
    /// </summary>
    public bool CleanupOrphanedSchedules { get; set; } = true;

    /// <summary>
    /// Whether to resolve duplicate schedules automatically
    /// </summary>
    public bool ResolveDuplicates { get; set; } = true;

    /// <summary>
    /// Whether to perform validation only (dry run)
    /// </summary>
    public bool ValidationOnly { get; set; } = false;

    /// <summary>
    /// Maximum number of concurrent operations
    /// </summary>
    public int MaxConcurrency { get; set; } = 5;
}

/// <summary>
/// Types of synchronization operations
/// </summary>
public enum SynchronizationType
{
    /// <summary>
    /// Full synchronization - create, update, and cleanup
    /// </summary>
    Full,

    /// <summary>
    /// Incremental synchronization - only process changes
    /// </summary>
    Incremental,

    /// <summary>
    /// Validation only - detect changes without making modifications
    /// </summary>
    ValidationOnly,

    /// <summary>
    /// Cleanup only - remove orphaned schedules
    /// </summary>
    CleanupOnly
}

/// <summary>
/// Command to synchronize a single journal schedule
/// </summary>
public class SynchronizeJournalScheduleCommand
{
    /// <summary>
    /// Journal to synchronize
    /// </summary>
    public JournalScheduleInfo Journal { get; set; } = new();

    /// <summary>
    /// Previous journal state for change detection
    /// </summary>
    public JournalScheduleInfo? PreviousJournal { get; set; }

    /// <summary>
    /// Type of operation being performed
    /// </summary>
    public JournalOperationType OperationType { get; set; }
}

/// <summary>
/// Command to detect schedule changes
/// </summary>
public class DetectScheduleChangesCommand
{
    /// <summary>
    /// Journals to check for changes
    /// </summary>
    public List<JournalScheduleInfo> Journals { get; set; } = new();

    /// <summary>
    /// Whether to include detailed change information
    /// </summary>
    public bool IncludeDetails { get; set; } = true;
}

/// <summary>
/// Command to validate for duplicate schedules
/// </summary>
public class ValidateDuplicateSchedulesCommand
{
    /// <summary>
    /// Whether to include task information in the validation
    /// </summary>
    public bool IncludeTaskDetails { get; set; } = true;

    /// <summary>
    /// Whether to automatically resolve duplicates
    /// </summary>
    public bool AutoResolve { get; set; } = false;
}

/// <summary>
/// Command to cleanup orphaned schedules
/// </summary>
public class CleanupOrphanedSchedulesCommand
{
    /// <summary>
    /// Currently active journals
    /// </summary>
    public List<JournalScheduleInfo> ActiveJournals { get; set; } = new();

    /// <summary>
    /// Whether to actually delete orphaned schedules (default: false for safety)
    /// </summary>
    public bool PerformActualDeletion { get; set; } = false;

    /// <summary>
    /// Grace period before considering a schedule orphaned
    /// </summary>
    public TimeSpan GracePeriod { get; set; } = TimeSpan.FromDays(7);
}
