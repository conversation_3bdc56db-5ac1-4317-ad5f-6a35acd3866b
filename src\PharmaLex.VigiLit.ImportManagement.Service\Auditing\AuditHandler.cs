﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.ImportManagement.Service.Auditing;

internal class AuditHandler : IAuditHandler
{
    private readonly ILogger<AuditHandler> _logger;

    private readonly IImportingImportRepository _importRepository;

    public AuditHandler(ILogger<AuditHandler> logger, IImportingImportRepository importRepository)
    {
        _logger = logger;
        _importRepository = importRepository;
    }

    public async Task Consume(StatusChangedEvent statusChangedEvent)
    {
        if (statusChangedEvent.CorrelationId == Guid.Empty)
        {
            _logger.LogWarning("CorrelationId is empty: {CorrelationId}", statusChangedEvent.CorrelationId);
        }

        try
        {
            var importRecord = await _importRepository.GetByCorrelationId(statusChangedEvent.CorrelationId);
            _logger.LogInformation("Import Status: {ImportStatus} for CorrelationId : {CorrelationId}", statusChangedEvent.Message,
                statusChangedEvent.CorrelationId);

            if (ShouldAssignEndDate(statusChangedEvent.Message))
            {
                importRecord.EndDate = statusChangedEvent.TimeCreated;
                importRecord.ImportDate = statusChangedEvent.TimeCreated;
            }

            importRecord.Message = statusChangedEvent.Message;
            importRecord.LastUpdatedBy = statusChangedEvent.User;
            await _importRepository.SaveChangesAsync();
        }

        catch (InvalidOperationException ex)
        {
            _logger.LogError(ex, "Correlation Id: {CorrelationId} not found or multiple with same Id", statusChangedEvent.CorrelationId);
        }
    }

    private static readonly List<string> EndDateAssignableMessage =
    [
        "Completed",
        "PhlexVision extraction failed",
        "Completed with failures",
        "Completed with no matches"
    ];

    private static bool ShouldAssignEndDate(string message)
    {
        return EndDateAssignableMessage.Contains(message, StringComparer.OrdinalIgnoreCase);
    }
}