﻿using PharmaLex.VigiLit.Core.Auditing;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.Auditing.Client
{
    public class AuditClient : IAuditClient
    {
        private readonly IAuditHandler _auditHandler;

        public AuditClient(IAuditHandler auditHandler)
        {
            _auditHandler = auditHandler;
        }

        public async Task Send(StatusChangedEvent statusChangedEvent)
        {
            await _auditHandler.Consume(statusChangedEvent);
        }
    }
}