using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Helpers;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class RestoreSchedulesCommandHandler : IRestoreSchedulesCommandHandler
{
    private readonly ILogger<RestoreSchedulesCommandHandler> _logger;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyWebhookService _webhookService;
    private readonly IScrapingConfigurationService _configurationService;

    public RestoreSchedulesCommandHandler(
        ILogger<RestoreSchedulesCommandHandler> logger,
        IApifyTaskService taskService,
        IApifyScheduleService scheduleService,
        IApifyWebhookService webhookService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _taskService = taskService;
        _scheduleService = scheduleService;
        _webhookService = webhookService;
        _configurationService = configurationService;
    }

    public async Task<RestoreSchedulesResult> Consume(RestoreSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();

        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            result.JournalsProcessed = command.Journals.Count;

            var journalGroups = command.Journals
                .GroupBy(j => j.CronExpression)
                .ToList();

            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Journals: {Journals}, Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.JournalsProcessed, result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, "Failed to restore schedules");
            result.Errors.Add($"Failed to restore schedules: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, JournalScheduleInfo> group,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();

        try
        {
            // Get existing schedules - reusing the same pattern as CreateOrUpdateScheduleCommandHandler
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var existingSchedule = schedules?.Data.Items.Find(x => x.CronExpression == cronExpression);

            if (existingSchedule != null)
            {
                // Schedule exists - check for duplicates and handle accordingly
                var duplicateSchedules = schedules?.Data.Items.Where(s => s.CronExpression == cronExpression).ToList() ?? new List<Apify.SDK.Model.GetListOfSchedulesResponseDataItems>();

                if (duplicateSchedules.Count > 1)
                {
                    result.Messages.Add($"Warning: Found {duplicateSchedules.Count} duplicate schedules for cron '{cronExpression}'. Using schedule '{existingSchedule.Id}' and ignoring others.");
                    _logger.LogWarning("Found {DuplicateCount} duplicate schedules for cron '{CronExpression}'. Using schedule '{ScheduleId}'",
                        duplicateSchedules.Count, cronExpression, existingSchedule.Id);
                }

                // Try to update existing schedule with group task - similar to UpdateTaskAsync pattern
                var groupTaskId = await UpdateOrCreateGroupTaskForScheduleAsync(existingSchedule, journals, result, cancellationToken);

                if (!string.IsNullOrEmpty(groupTaskId) && !string.IsNullOrEmpty(webhookUrl))
                {
                    await _webhookService.CreateWebhookForTaskAsync(groupTaskId, webhookUrl, cancellationToken);
                    result.WebhooksCreated++;
                    result.Messages.Add($"Created webhook for task '{groupTaskId}'");
                }
            }
            else
            {
                // No existing schedule - create new one using the same pattern as CreateScheduleWithTaskAsync
                var groupTaskId = await CreateScheduleWithGroupTaskAsync(journals, cronExpression, webhookUrl, result, cancellationToken);

                if (string.IsNullOrEmpty(groupTaskId))
                {
                    result.Errors.Add($"Failed to create schedule and task for cron '{cronExpression}'");
                }
            }

            _logger.LogInformation("Successfully processed schedule group for cron '{CronExpression}' with {JournalCount} journals",
                cronExpression, journals.Count);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to process schedule group for cron '{cronExpression}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
        }
    }

    /// <summary>
    /// Updates existing schedule with group task or creates new task if none exists.
    /// Similar to UpdateTaskAsync pattern in CreateOrUpdateScheduleCommandHandler.
    /// </summary>
    private async Task<string?> UpdateOrCreateGroupTaskForScheduleAsync(
        Apify.SDK.Model.GetListOfSchedulesResponseDataItems existingSchedule,
        List<JournalScheduleInfo> journals,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        // Check if schedule has existing tasks - same pattern as UpdateTaskAsync
        var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(existingSchedule.Id, cancellationToken);

        if (taskIds == null || taskIds.Count == 0 || string.IsNullOrEmpty(taskIds[0]))
        {
            // No existing task - create new group task and link to schedule
            var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", existingSchedule.CronExpression);
            var groupTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

            if (!string.IsNullOrEmpty(groupTaskId))
            {
                await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, groupTaskId, cancellationToken);
                result.TasksCreated++;
                result.Messages.Add($"Created and linked task '{groupTaskName}' to existing schedule '{existingSchedule.Id}' for {journals.Count} journals");
                return groupTaskId;
            }
            else
            {
                _logger.LogWarning("Failed to create group task for existing schedule '{ScheduleId}'", existingSchedule.Id);
                return null;
            }
        }
        else
        {
            // Task exists - create updated group task to replace existing one
            var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group-updated", existingSchedule.CronExpression);
            var newTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

            if (!string.IsNullOrEmpty(newTaskId))
            {
                await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, newTaskId, cancellationToken);
                result.TasksCreated++;
                result.Messages.Add($"Updated existing schedule '{existingSchedule.Id}' with refreshed task '{groupTaskName}' for {journals.Count} journals");
                return newTaskId;
            }
            else
            {
                _logger.LogWarning("Failed to update group task for existing schedule '{ScheduleId}'", existingSchedule.Id);
                return taskIds[0]; // Return existing task ID as fallback
            }
        }
    }

    /// <summary>
    /// Creates new schedule with group task and webhook.
    /// Similar to CreateScheduleWithTaskAsync pattern in CreateOrUpdateScheduleCommandHandler.
    /// </summary>
    private async Task<string?> CreateScheduleWithGroupTaskAsync(
        List<JournalScheduleInfo> journals,
        string cronExpression,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        // Create group task first
        var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", cronExpression);
        var groupTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

        if (string.IsNullOrEmpty(groupTaskId))
        {
            var errorMessage = $"Failed to create group task for cron '{cronExpression}': Task ID was null or empty";
            _logger.LogWarning(LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            return null;
        }

        result.TasksCreated++;
        result.Messages.Add($"Created new group task '{groupTaskName}' for {journals.Count} journals");

        // Create schedule for the group task - same pattern as CreateScheduleWithTaskAsync
        var groupScheduleName = ScrapingHelper.GenerateUniqueName("schedule-group", cronExpression);
        await _scheduleService.CreateScheduleForTaskAsync(groupTaskId, groupScheduleName, cronExpression, cancellationToken);
        result.SchedulesCreated++;
        result.Messages.Add($"Created new group schedule '{groupScheduleName}' for cron '{cronExpression}' with {journals.Count} journals");

        // Create webhook if URL is provided - same pattern as CreateScheduleWithTaskAsync
        if (!string.IsNullOrEmpty(webhookUrl))
        {
            await _webhookService.CreateWebhookForTaskAsync(groupTaskId, webhookUrl, cancellationToken);
            result.WebhooksCreated++;
            result.Messages.Add($"Created webhook for group task '{groupTaskId}' covering {journals.Count} journals");
        }
        else
        {
            _logger.LogWarning("Webhook URL not provided for group task '{GroupTaskId}'", groupTaskId);
        }

        return groupTaskId;
    }
}
