using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class RestoreSchedulesCommandHandler : IRestoreSchedulesCommandHandler
{
    private readonly ILogger<RestoreSchedulesCommandHandler> _logger;
    private readonly IScheduleManagementService _scheduleManagementService;
    private readonly IScrapingConfigurationService _configurationService;

    public RestoreSchedulesCommandHandler(
        ILogger<RestoreSchedulesCommandHandler> logger,
        IScheduleManagementService scheduleManagementService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _scheduleManagementService = scheduleManagementService;
        _configurationService = configurationService;
    }

    public async Task<RestoreSchedulesResult> Consume(RestoreSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();

        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            result.JournalsProcessed = command.Journals.Count;

            var journalGroups = command.Journals
                .GroupBy(j => j.CronExpression)
                .ToList();

            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Journals: {Journals}, Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.JournalsProcessed, result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, "Failed to restore schedules");
            result.Errors.Add($"Failed to restore schedules: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, JournalScheduleInfo> group,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();

        try
        {
            var existingSchedule = await _scheduleManagementService.FindExistingScheduleAsync(cronExpression, cancellationToken);

            if (existingSchedule is not null)
            {
                var allSchedules = await _scheduleManagementService.GetAllSchedulesAsync(cancellationToken);
                var duplicateSchedules = allSchedules?.Data.Items.Where(s => s.CronExpression == cronExpression).ToList() ?? new List<Apify.SDK.Model.GetListOfSchedulesResponseDataItems>();

                if (duplicateSchedules.Count > 1)
                {
                    result.Messages.Add($"Warning: Found {duplicateSchedules.Count} duplicate schedules for cron '{cronExpression}'. Using schedule '{existingSchedule.Id}' and ignoring others.");
                    _logger.LogWarning("Found {DuplicateCount} duplicate schedules for cron '{CronExpression}'. Using schedule '{ScheduleId}'",
                        duplicateSchedules.Count, cronExpression, existingSchedule.Id);
                }

                var groupTaskId = await _scheduleManagementService.UpdateScheduleWithGroupTaskAsync(existingSchedule, journals, cancellationToken);

                if (!string.IsNullOrEmpty(groupTaskId))
                {
                    result.TasksCreated++;
                    result.Messages.Add($"Updated existing schedule '{existingSchedule.Id}' with group task for {journals.Count} journals");

                    if (await _scheduleManagementService.CreateWebhookIfConfiguredAsync(groupTaskId, cancellationToken))
                    {
                        result.WebhooksCreated++;
                        result.Messages.Add($"Created webhook for task '{groupTaskId}'");
                    }
                }
                else
                {
                    result.Errors.Add($"Failed to update existing schedule '{existingSchedule.Id}' for cron '{cronExpression}'");
                }
            }
            else
            {
                var groupTaskId = await _scheduleManagementService.CreateScheduleWithGroupTaskAsync(journals, cronExpression, cancellationToken);

                if (!string.IsNullOrEmpty(groupTaskId))
                {
                    result.TasksCreated++;
                    result.SchedulesCreated++;
                    result.Messages.Add($"Created new group schedule and task for cron '{cronExpression}' with {journals.Count} journals");

                    if (await _scheduleManagementService.CreateWebhookIfConfiguredAsync(groupTaskId, cancellationToken))
                    {
                        result.WebhooksCreated++;
                        result.Messages.Add($"Created webhook for task '{groupTaskId}'");
                    }
                }
                else
                {
                    result.Errors.Add($"Failed to create schedule and task for cron '{cronExpression}'");
                }
            }

            _logger.LogInformation("Successfully processed schedule group for cron '{CronExpression}' with {JournalCount} journals",
                cronExpression, journals.Count);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to process schedule group for cron '{cronExpression}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
        }
    }
}
