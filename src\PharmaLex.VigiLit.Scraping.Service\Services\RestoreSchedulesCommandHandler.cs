using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Helpers;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class RestoreSchedulesCommandHandler : IRestoreSchedulesCommandHandler
{
    private readonly ILogger<RestoreSchedulesCommandHandler> _logger;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyWebhookService _webhookService;
    private readonly IScrapingConfigurationService _configurationService;

    public RestoreSchedulesCommandHandler(
        ILogger<RestoreSchedulesCommandHandler> logger,
        IApifyTaskService taskService,
        IApifyScheduleService scheduleService,
        IApifyWebhookService webhookService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _taskService = taskService;
        _scheduleService = scheduleService;
        _webhookService = webhookService;
        _configurationService = configurationService;
    }

    public async Task<RestoreSchedulesResult> Consume(RestoreSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();

        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            result.JournalsProcessed = command.Journals.Count;

            var journalGroups = command.Journals
                .GroupBy(j => j.CronExpression)
                .ToList();

            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Journals: {Journals}, Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.JournalsProcessed, result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, "Failed to restore schedules");
            result.Errors.Add($"Failed to restore schedules: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, JournalScheduleInfo> group,
        string webhookUrl,
        RestoreSchedulesResult result,
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();

        try
        {
            // Check for existing schedules with the same cron expression
            var existingSchedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var matchingSchedules = existingSchedules?.Data.Items.Where(s => s.CronExpression == cronExpression).ToList() ?? new List<Apify.SDK.Model.GetListOfSchedulesResponseDataItems>();

            if (matchingSchedules.Count > 0)
            {
                _logger.LogInformation("Found {ExistingCount} existing schedule(s) for cron '{CronExpression}'. Checking if update is needed.",
                    matchingSchedules.Count, cronExpression);

                // Use the first existing schedule (in case of duplicates, we'll work with the oldest one)
                var existingSchedule = matchingSchedules.OrderBy(s => s.CreatedAt).First();

                if (matchingSchedules.Count > 1)
                {
                    result.Messages.Add($"Warning: Found {matchingSchedules.Count} duplicate schedules for cron '{cronExpression}'. Using schedule '{existingSchedule.Id}' and ignoring others.");
                    _logger.LogWarning("Found {DuplicateCount} duplicate schedules for cron '{CronExpression}'. Using schedule '{ScheduleId}'",
                        matchingSchedules.Count, cronExpression, existingSchedule.Id);
                }

                // Check if the existing schedule has associated tasks
                var existingTaskIds = await _taskService.GetTaskIdsByScheduleIdAsync(existingSchedule.Id, cancellationToken);

                if (existingTaskIds == null || existingTaskIds.Count == 0)
                {
                    // Schedule exists but no task - create and link a new task
                    var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", cronExpression);
                    var groupTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

                    if (!string.IsNullOrEmpty(groupTaskId))
                    {
                        await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, groupTaskId, cancellationToken);
                        result.TasksCreated++;
                        result.Messages.Add($"Created and linked task '{groupTaskName}' to existing schedule '{existingSchedule.Id}' for {journals.Count} journals");

                        // Create webhook for the new task if webhook URL is provided
                        if (!string.IsNullOrEmpty(webhookUrl))
                        {
                            await _webhookService.CreateWebhookForTaskAsync(groupTaskId, webhookUrl, cancellationToken);
                            result.WebhooksCreated++;
                            result.Messages.Add($"Created webhook for task '{groupTaskId}'");
                        }
                    }
                    else
                    {
                        result.Errors.Add($"Failed to create task for existing schedule '{existingSchedule.Id}' with cron '{cronExpression}'");
                    }
                }
                else
                {
                    // Schedule and task(s) already exist - update the task with current journal URLs
                    var primaryTaskId = existingTaskIds[0];

                    // Create a new updated task to replace the existing one (simpler than trying to update URLs)
                    var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group-updated", cronExpression);
                    var newTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

                    if (!string.IsNullOrEmpty(newTaskId))
                    {
                        await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, newTaskId, cancellationToken);
                        result.TasksCreated++;
                        result.Messages.Add($"Updated existing schedule '{existingSchedule.Id}' with refreshed task '{groupTaskName}' for {journals.Count} journals");

                        // Create webhook for the new task if webhook URL is provided
                        if (!string.IsNullOrEmpty(webhookUrl))
                        {
                            await _webhookService.CreateWebhookForTaskAsync(newTaskId, webhookUrl, cancellationToken);
                            result.WebhooksCreated++;
                            result.Messages.Add($"Created webhook for updated task '{newTaskId}'");
                        }
                    }
                    else
                    {
                        result.Errors.Add($"Failed to update task for existing schedule '{existingSchedule.Id}' with cron '{cronExpression}'");
                    }
                }
            }
            else
            {
                // No existing schedule found - create new schedule, task, and webhook
                var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", cronExpression);
                var groupScheduleName = ScrapingHelper.GenerateUniqueName("schedule-group", cronExpression);

                // Create one group task for all journals in this cron group
                var groupTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);
                if (string.IsNullOrEmpty(groupTaskId))
                {
                    var errorMessage = $"Failed to create group task for cron '{cronExpression}': Task ID was null or empty";
                    _logger.LogWarning(LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                    return;
                }

                result.TasksCreated++;
                result.Messages.Add($"Created new group task '{groupTaskName}' for {journals.Count} journals");

                // Create one schedule for the group task
                await _scheduleService.CreateScheduleForTaskAsync(groupTaskId, groupScheduleName, cronExpression, cancellationToken);
                result.SchedulesCreated++;
                result.Messages.Add($"Created new group schedule '{groupScheduleName}' for cron '{cronExpression}' with {journals.Count} journals");

                // Create one webhook for the group task
                if (!string.IsNullOrEmpty(webhookUrl))
                {
                    await _webhookService.CreateWebhookForTaskAsync(groupTaskId, webhookUrl, cancellationToken);
                    result.WebhooksCreated++;
                    result.Messages.Add($"Created webhook for group task '{groupTaskId}' covering {journals.Count} journals");
                }
            }

            _logger.LogInformation("Successfully processed schedule group for cron '{CronExpression}' with {JournalCount} journals",
                cronExpression, journals.Count);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to process schedule group for cron '{cronExpression}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
        }
    }
}
