﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Phlex.Core.Apify\src\Phlex.Core.Apify.Webhook\Phlex.Core.Apify.Webhook.csproj" />
    <ProjectReference Include="..\..\..\Phlex.Core.Apify\src\Phlex.Core.Apify\Phlex.Core.Apify.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Client\PharmaLex.VigiLit.DataExtraction.Client.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Scraping.Client\PharmaLex.VigiLit.Scraping.Client.csproj" />
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="14.0.0" />
	<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
	<PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
  </ItemGroup>

</Project>
