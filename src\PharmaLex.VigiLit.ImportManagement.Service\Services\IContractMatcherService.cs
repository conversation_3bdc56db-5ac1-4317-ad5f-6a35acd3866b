﻿using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

internal interface IContractMatcherService
{
    /// <summary>
    /// Finds contract versions for which their criteria match the given reference.
    /// </summary>
    /// <param name="matchReference">The lightweight reference object to match against contract versions.</param>
    /// <returns>A task that represents the asynchronous operation, containing a list of matching contract versions.</returns>
    public Task<List<ContractVersion>> FindMatchingContractVersions(MatchReference matchReference);

    // Shouldn't be here
    Task SendToAi(Reference reference, IList<ImportContract> importContracts);
}