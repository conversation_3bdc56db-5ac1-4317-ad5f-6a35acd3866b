using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

/// <summary>
/// Service for synchronizing journal schedule settings with APIFY, ensuring no duplicate cron jobs
/// and maintaining consistency between local database and APIFY schedules.
/// </summary>
public interface IApifyScheduleSynchronizationService
{
    /// <summary>
    /// Synchronizes all journal schedules with APIFY, checking for existing records and preventing duplicates.
    /// </summary>
    /// <param name="journals">The journals to synchronize</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Synchronization result with details of operations performed</returns>
    Task<ScheduleSynchronizationResult> SynchronizeAllSchedulesAsync(
        IEnumerable<JournalScheduleInfo> journals, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Synchronizes a specific journal schedule with APIFY, detecting changes and updating accordingly.
    /// </summary>
    /// <param name="journal">The journal to synchronize</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Synchronization result for the specific journal</returns>
    Task<JournalSynchronizationResult> SynchronizeJournalScheduleAsync(
        JournalScheduleInfo journal, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Detects changes between local journal settings and APIFY schedules.
    /// </summary>
    /// <param name="journals">Local journals to compare</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Change detection result with identified differences</returns>
    Task<ScheduleChangeDetectionResult> DetectScheduleChangesAsync(
        IEnumerable<JournalScheduleInfo> journals, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates that no duplicate schedules exist in APIFY for the same cron expression.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result with any duplicates found</returns>
    Task<DuplicateValidationResult> ValidateNoDuplicateSchedulesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes orphaned schedules from APIFY that no longer have corresponding journals.
    /// </summary>
    /// <param name="activeJournals">Currently active journals</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cleanup result with details of removed schedules</returns>
    Task<ScheduleCleanupResult> CleanupOrphanedSchedulesAsync(
        IEnumerable<JournalScheduleInfo> activeJournals, 
        CancellationToken cancellationToken = default);
}
