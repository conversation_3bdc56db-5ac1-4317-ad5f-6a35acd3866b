﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using PharmaLex.VigiLit.Core.Auditing;
using PharmaLex.VigiLit.Core.CrossCutting;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using PharmaLex.VigiLit.Test.Framework.Fakes;
using System.Net;
using System.Text.Json;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.PhlexVision;

public class PhlexVisionServiceTests
{
    private const string ConsumerKeyHeaderValue = "PhlexVision:ConsumerKeyHeaderValue";
    private const string PhlexVisionUrl = "PhlexVision:Url";
    private const string SecretValue = "PhlexVision:Secret";
    private const string PhlexVisionCallbackBaseUrl = "PhlexVision:CallbackBaseUrl";
    private const string OpenAiConfigId = "PhlexVision:OpenAiConfigId";

    private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler = new();
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<IDocumentProcessModelFactory> _mockDocumentProcessModelFactory = new();
    private readonly Mock<ICrossCuttingContext<PhlexVisionService>> _mockCrossCuttingContext = new();

    private readonly IPhlexVisionService _visionService;

    public PhlexVisionServiceTests()
    {
        var fakeHttpClientFactory = new FakeHttpClientFactory(_mockHttpMessageHandler.Object);
        _mockConfiguration = new();
        Mock<IAuditClient> mockAuditClient = new();
        var mockLogger = new Mock<ILogger<PhlexVisionService>>();

        _mockCrossCuttingContext.Setup(x => x.Logger).Returns(mockLogger.Object);

        _visionService = new PhlexVisionService(mockLogger.Object, fakeHttpClientFactory, _mockConfiguration.Object, mockAuditClient.Object, _mockDocumentProcessModelFactory.Object, _mockCrossCuttingContext.Object);
    }

    [Fact]
    public async Task Given_request_When_requested_then_request_is_made()
    {
        // Arrange
        var request = new ExtractRequest
        {
            BatchId = "--DocumentId--",
            FileName = @"\PATH\FILENAME",
        };

        var fakeHttpClientFactory = new FakeHttpClientFactory(_mockHttpMessageHandler.Object);

        _mockConfiguration.Setup(x => x[PhlexVisionUrl]).Returns("http://localhost:1234");
        _mockConfiguration.Setup(x => x[SecretValue]).Returns("D66D6C6C1BD24CCDBA6109DD2D09E05F");
        _mockConfiguration.Setup(x => x[ConsumerKeyHeaderValue]).Returns("mock");
        _mockConfiguration.Setup(x => x[PhlexVisionCallbackBaseUrl]).Returns("http://callback.com/");
        _mockConfiguration.Setup(x => x[OpenAiConfigId]).Returns("5d4fe566-df27-41be-b4e3-50bb73e95fb1");

        ArrangeSuccessfulApiCall();

        // Act
        await _visionService.RequestDataExtraction(request);

        // Assert
        Assert.True(true); // NEED TO ADD SOME ASSERTS AND SOME SETUP FOR THE HttpRequestMessage in the arrangement section. Will throw is anything wrong so far


    }

    private void ArrangeSuccessfulApiCall()
    {
        var expectedResponse = new HttpResponseMessage(HttpStatusCode.OK);
        expectedResponse.Headers.TryAddWithoutValidation("X-Correlation-ID", new List<string?> { "d8799676-b701-48e9-823b-d5509f78ad8e" });

        _mockDocumentProcessModelFactory
            .Setup(x => x.GetDocumentProcessModel(It.IsAny<string>(), It.IsAny<ExtractRequest>()))
            .Returns(string.Empty);

        var msg = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(JsonSerializer.Serialize(expectedResponse)),
        };

        msg.Headers.TryAddWithoutValidation("X-Correlation-ID", new List<string?> { "d8799676-b701-48e9-823b-d5509f78ad8e" });

        _mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(
                    m =>
                        m.Method == HttpMethod.Post &&
                        m.RequestUri == new Uri("http://localhost:1234")
                    ),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(msg);
    }
}
