﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Core.Auditing;

namespace PharmaLex.VigiLit.Core.CrossCutting;

public interface ICrossCuttingContext<out T> where T : class
{
    /// <summary>
    /// Gets the logger.
    /// </summary>
    /// <value>
    /// The logger.
    /// </value>
    ILogger<T> Logger { get; }

    /// <summary>
    /// Gets the audit client.
    /// </summary>
    /// <value>
    /// The audit client.
    /// </value>
    IAuditClient AuditClient { get; }

    /// <summary>
    /// Gets the time provider.
    /// </summary>
    /// <value>
    /// The time provider.
    /// </value>
    TimeProvider TimeProvider { get; }

    /// <summary>
    /// Gets the configuration.
    /// </summary>
    /// <value>
    /// The configuration.
    /// </value>
    IConfiguration Configuration { get; }
}