﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Time.Testing;
using Moq;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.ImportManagement.Service.Services;
using PharmaLex.VigiLit.Test.Fakes.Entities;
using IReferenceClassificationRepository = PharmaLex.VigiLit.ImportManagement.Service.Repositories.IReferenceClassificationRepository;
using IReferenceHistoryActionRepository = PharmaLex.VigiLit.ImportManagement.Service.Repositories.IReferenceHistoryActionRepository;
using IReferenceRepository = PharmaLex.VigiLit.ImportManagement.Service.Repositories.IReferenceRepository;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class UnIndexedReferenceImporterTests
{
    private readonly IUnIndexedReferenceImporter _unIndexedReferenceImporter;
    private readonly Mock<IImportReferenceRepository> _mockImportReferenceRepository = new();
    private readonly Mock<IContractMatcherService> _mockContractMatcherService = new();

    private readonly Mock<IImportingImportRepository> _mockImportRepository = new();
    private readonly Mock<IImportingImportContractRepository> _mockImportContractRepository = new();
    private readonly Mock<IReferenceRepository> _mockReferenceRepository = new();
    private readonly Mock<IReferenceClassificationRepository> _mockReferenceClassificationRepository = new();
    private readonly Mock<IReferenceHistoryActionRepository> _mockReferenceHistoryActionRepository = new();
    private readonly Mock<IImportingImportContractReferenceClassificationRepository> _mockImportContractReferenceClassificationRepository = new();
    private readonly Mock<IAuditHandler> _mockAuditHandler = new();
    private readonly Mock<ILogger<UnIndexedReferenceImporter>> _mockLogger = new();
    private readonly Mock<IDocumentLocationService> _documentLocationService = new();

    private readonly FakeTimeProvider _fakeTimeProvider = new();

    public UnIndexedReferenceImporterTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ImportReferenceMappingProfile());
        });

        var mapper = mapperConfig.CreateMapper();

        _unIndexedReferenceImporter = new UnIndexedReferenceImporter(
            _mockImportRepository.Object,
            _mockImportContractRepository.Object,
            _mockImportReferenceRepository.Object,
            _mockReferenceRepository.Object,
            _mockReferenceClassificationRepository.Object,
            _mockReferenceHistoryActionRepository.Object,
            _mockImportContractReferenceClassificationRepository.Object,
            _mockContractMatcherService.Object,
            mapper,
            _fakeTimeProvider,
            _mockAuditHandler.Object,
            _mockLogger.Object,
            _documentLocationService.Object);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_DoesNot_SaveRecord_When_ReferenceNotMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).Returns(Task.FromResult(default(ImportReference?)));

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Never);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_SavesRecord_When_ReferenceMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).ReturnsAsync(new ImportReference { StatusType = ImportReferenceStatusType.Queued });

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<MatchReference>()))
            .ReturnsAsync(GetTestContractVersions());

        _mockImportRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).ReturnsAsync(new Import());
        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_Saves_Import()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued())
                                                        .ReturnsAsync(GetTestImportReference());

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<MatchReference>()))
            .ReturnsAsync(GetTestContractVersions());
        _mockImportRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).ReturnsAsync(new Import());

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockImportContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockImportContractRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockReferenceHistoryActionRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        _mockImportContractReferenceClassificationRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_UpdateImportReferenceRepository_When_ReferenceMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).ReturnsAsync(new ImportReference { StatusType = ImportReferenceStatusType.Queued });

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<MatchReference>()))
            .ReturnsAsync(GetTestContractVersions());
        _mockImportRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).ReturnsAsync(new Import { ImportStatusType = ImportStatusType.Completed });
        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task ProcessEnqueuedImports_SavesImportContractStatusAsCompleted_When_ReferenceMatched()
    {
        // Arrange
        _mockImportReferenceRepository.Setup(x => x.GetNextQueued()).ReturnsAsync(new ImportReference { StatusType = ImportReferenceStatusType.Queued });

        _mockContractMatcherService.Setup(x => x.FindMatchingContractVersions(It.IsAny<MatchReference>()))
            .ReturnsAsync(GetTestContractVersions());
        _mockImportRepository.Setup(x => x.GetByCorrelationId(It.IsAny<Guid>())).ReturnsAsync(new Import());

        // Act
        await _unIndexedReferenceImporter.ProcessEnqueuedImports();

        // Assert
        _mockImportContractRepository.Verify(x => x.Add(It.Is<ImportContract>(i => i.ImportContractStatusType == ImportContractStatusType.Completed)));
        _mockImportReferenceRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    private static ImportReference GetTestImportReference()
    {
        return new FakeImportReference(100);
    }

    private static List<ContractVersion> GetTestContractVersions()
    {
        return new List<ContractVersion>
        {
            new ContractVersion( GetTestContract(), "some substance" )
        };
    }

    private static Contract GetTestContract()
    {
        return new FakeContract(200, GetTestSubstance(), new Project(), DateTime.Now, new List<ContractVersion>());
    }

    private static Substance GetTestSubstance()
    {
        return new FakeSubstance(300, "Test substance", "ch");
    }
}