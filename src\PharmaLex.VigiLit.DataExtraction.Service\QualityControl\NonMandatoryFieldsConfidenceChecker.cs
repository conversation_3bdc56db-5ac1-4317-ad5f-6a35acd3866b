﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using System.Reflection;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class NonMandatoryFieldsConfidenceChecker : IExtractionValidator
{
    private readonly ILogger<NonMandatoryFieldsConfidenceChecker> _logger;
    private readonly List<PropertyInfo> _nonMandatoryFields = [];
    private readonly float _nonMandatoryFieldMinimumConfidenceLevel;
    public NonMandatoryFieldsConfidenceChecker(ILogger<NonMandatoryFieldsConfidenceChecker> logger, IConfiguration configuration)
    {
        PopulatePropertyInfoCollections();
        _logger = logger;
        _nonMandatoryFieldMinimumConfidenceLevel = configuration.GetValue<float>("DataExtraction:NonMandatoryFieldMinimumConfidenceLevel");
    }

    public bool IsValid(ExtractedReference extractedReference)
    {
        return DoFieldsPassConfidenceLevel(extractedReference, _nonMandatoryFields, _nonMandatoryFieldMinimumConfidenceLevel);
    }

    private bool DoFieldsPassConfidenceLevel(ExtractedReference reference, List<PropertyInfo> fields, float confidenceLevel)
    {
        foreach (var field in fields)
        {
            if (field.GetValue(reference) is ICollection<IExtractedProperty> items)
            {
                if (items.AnyFailConfidence(confidenceLevel))
                {
                    _logger.LogInformation("Property: {FieldName} has low confidence.", field.Name);
                    return false;
                }
            }
            else
            {
                var extractedProperty = field.GetValue(reference) as IExtractedProperty;
                if (extractedProperty is null || extractedProperty.FailConfidence(confidenceLevel))
                {
                    _logger.LogInformation("Property: {FieldName} has low confidence.", field.Name);
                    return false;
                }
            }
        }

        return true;
    }
    private void PopulatePropertyInfoCollections()
    {
        var properties = typeof(ExtractedReference).GetProperties();

        _nonMandatoryFields.AddRange(
            properties.Where(info => !Attribute.IsDefined(info, typeof(MandatoryAttribute)))
        );
    }
}