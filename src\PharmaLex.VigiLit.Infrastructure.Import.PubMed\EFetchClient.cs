﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Extensions;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.ESearch;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.PubMed;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using static PharmaLex.VigiLit.Infrastructure.Import.PubMed.Constants;
using EFetchResult = PharmaLex.VigiLit.Infrastructure.Import.PubMed.Models.EFetch.EFetchResult;

namespace PharmaLex.VigiLit.Infrastructure.Import.PubMed;

public class EFetchClient : PubMedClientBase, IEFetchClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<EFetchClient> _logger;

    public EFetchClient(HttpClient httpClient, ILoggerFactory loggerFactory, IConfiguration configuration)
        : base(configuration)
    {
        _httpClient = httpClient;
        _logger = loggerFactory.CreateLogger<EFetchClient>();
    }

    public async Task<EFetchResult> GetArticleSet(ESearchResult searchResult, DateTime timeout)
    {
        var results = new EFetchResult();

        // Retrieve data in batches of 250
        int retmax = 250;

        // Results are capped at 10k (9998 is the highest retStart that works)
        int cap = 9999;

        for (int retstart = 0; retstart < Math.Min(searchResult.Count, cap); retstart += retmax)
        {
            CheckTimeout(timeout);

            PubmedArticleSet batch = await GetArticleSetBatch(searchResult.QueryKey, searchResult.WebEnv, retstart, retmax);

            // If one batch failed, fail the whole thing.
            if (batch == null)
            {
                _logger.LogWarning("Batch is null. Failing entire fetch.");

                return new EFetchResult()
                {
                    BatchFailed = true
                };
            }

            results.ArticleSet.AddRange(batch);
        }

        // Flag if capped
        if (searchResult.Count > cap)
        {
            results.ResultsCapped = true;
        }

        return results;
    }

    private void CheckTimeout(DateTime timeout)
    {
        if (DateTime.UtcNow >= timeout)
        {
            throw new TimeoutException("Timeout from EFetchClient.");
        }
    }

    private async Task<PubmedArticleSet> GetArticleSetBatch(int queryKey, string webEnv, int retstart, int retmax)
    {
        await RateLimiter();

        string queryString = BuildQueryString(queryKey, webEnv, retstart, retmax);

        _logger.LogInformation("eFetch: {Url}", _httpClient.BaseAddress + queryString);

        try
        {
            List<Retry> retries = new()
            {
                new Retry(1, 2),
                new Retry(2, 10),
                new Retry(3, 30)
            };

            using Stream tagsReplacedStream = await FetchWithRetriesRecursive(queryString, retries);

            PubmedArticleSet setBatch = Deserialize(tagsReplacedStream);
            return setBatch;
        }
        catch (Exception e)
        {
            _logger.LogWarning("GetArticleSetBatch returning null due to exception. \nError: {Error}", e.ToString());
            return null;
        }
    }

    private async Task<Stream> FetchWithRetriesRecursive(string queryString, List<Retry> retries)
    {
        try
        {
            using Stream stream = await _httpClient.GetStreamAsync(queryString);

            using StreamReader reader = new(stream);
            string text = await reader.ReadToEndAsync();

            string tagsReplaced = ReplaceTags(text);
            Stream tagsReplacedStream = tagsReplaced.ToStream();

            if (retries.Any(r => r.Done))
            {
                _logger.LogWarning("Recovered.");
            }

            return tagsReplacedStream;
        }
        catch (Exception e) when (e is HttpRequestException || e is IOException)
        {
            if (retries.Any(r => !r.Done))
            {
                Retry retry = retries.First(r => !r.Done);
                retry.Done = true;

                _logger.LogWarning("EFetch request failed: exceptionType={ExceptionType}, using retry behaviour. retry={Retry}/{TotalRetries}, delaySeconds={DelaySeconds}",
                    e.GetType().Name, retry.Number, retries.Count, retry.DelaySeconds);

                await Task.Delay(retry.DelaySeconds * 1000);

                return await FetchWithRetriesRecursive(queryString, retries);
            }
            else
            {
                throw;
            }
        }
    }

    private static string ReplaceTags(string textToBeModified)
    {
        List<(string pattern, string replacementText)> rules = new()
        {
            (pattern: @"<i>", replacementText: "##i##"),
            (pattern: @"</i>", replacementText: "##/i##"),
            (pattern: @"<b>", replacementText: "##b##"),
            (pattern: @"</b>", replacementText: "##/b##"),
            (pattern: @"<sub>", replacementText: "##sub##"),
            (pattern: @"</sub>", replacementText: "##/sub##"),
            (pattern: @"<sup>", replacementText: "##sup##"),
            (pattern: @"</sup>", replacementText: "##/sup##"),
            (pattern: @"<u>", replacementText: "##u##"),
            (pattern: @"</u>", replacementText: "##/u##")
        };

        string modifiedText = Utilities.RegexMultiReplace(textToBeModified, rules);

        return modifiedText;
    }

    private string BuildQueryString(int queryKey, string webEnv, int retstart, int retmax)
    {
        StringBuilder builder = new();
        builder.Append($"?db={DBNAME}");
        builder.Append($"&query_key={queryKey}");
        builder.Append($"&WebEnv={webEnv}");
        builder.Append($"&{RETMODE_XML}"); // The 15-Nov-2022 version of the api defaults to XML so this won't be necessary.
        builder.Append($"&retstart={retstart}");
        builder.Append($"&retmax={retmax}");
        builder.Append($"&api_key={PubMedApiKey}");

        return builder.ToString();
    }

    public PubmedArticleSet Deserialize(Stream stream)
    {
        using XmlReader reader = CreateXmlReader(stream);

        XmlSerializer serializer = new XmlSerializer(typeof(PubmedArticleSet));

        try
        {
            var articleSet = (PubmedArticleSet)serializer.Deserialize(reader);
            return articleSet;
        }
        catch (Exception ex)
        {
            _logger.LogError("Could not deserialize response from EFetch. \nError: {Error}", ex.ToString());
        }

        return null;
    }
}
