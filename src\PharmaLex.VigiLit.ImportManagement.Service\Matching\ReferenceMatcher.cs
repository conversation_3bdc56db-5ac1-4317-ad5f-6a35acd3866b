﻿using FuzzySharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Core.CrossCutting;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;

internal class ReferenceMatcher : IReferenceMatcher
{
    private readonly IAbstractTextSearcher _abstractTextSearcher;
    private readonly ILogger<ReferenceMatcher> _logger;
    private readonly int _fuzzySharpJournalMatchThreshold;

    public ReferenceMatcher(ICrossCuttingContext<ReferenceMatcher> ctx,
                            IAbstractTextSearcher abstractTextSearcher)
    {
        _abstractTextSearcher = abstractTextSearcher;
        _logger = ctx.Logger;
        _fuzzySharpJournalMatchThreshold = ctx.Configuration.GetValue<int>("DataExtraction:FuzzySharpJournalMatchThreshold");
    }

    public async Task<bool> Matches(MatchReference matchReference, IReadOnlyCollection<string> journalTitles, IScriptAdapter<bool> matchingConditionScript)
    {
        var journalTitle = matchReference.JournalTitle;
        var abstractText = matchReference.AbstractText;
        var fullText = matchReference.FullText;
        var fullUntranslatedText = matchReference.FullUntranslatedText;

        try
        {
            var doesSourceMatch = await DoesSourceMatch(matchingConditionScript, abstractText);
            var doesJournalMatch = DoesJournalTitleMatch(journalTitle, journalTitles);

            return doesJournalMatch && (doesSourceMatch || await DoesSourceMatch(matchingConditionScript, fullText) || await DoesSourceMatch(matchingConditionScript, fullUntranslatedText));
        }

        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in reference matching: {Error}", ex.ToString());
            return await Task.FromResult(false);
        }
    }

    private bool DoesJournalTitleMatch(string journalTitle, IReadOnlyCollection<string> journalTitles)
    {
        if (journalTitles.Count == 0)
        {
            return true;
        }

        var journalTitleUpper = journalTitle.ToUpper();
        var journalTitlesUpper = journalTitles.Select(x => x.ToUpper());

        var fuzzyMatchSuccess = journalTitlesUpper.Any(x => Fuzz.Ratio(journalTitleUpper, x) > _fuzzySharpJournalMatchThreshold);

        if (!fuzzyMatchSuccess)
        {
            _logger.LogWarning("Journal title: {JournalTitle} could not be matched.", journalTitle);
        }

        return fuzzyMatchSuccess;
    }

    private async Task<bool> DoesSourceMatch(IScriptAdapter<bool> matchingConditionScript, string sourceText)
    {
        _abstractTextSearcher.AbstractText = sourceText;
        var scriptResult = await matchingConditionScript.RunAsync(_abstractTextSearcher);
        return scriptResult;
    }
}