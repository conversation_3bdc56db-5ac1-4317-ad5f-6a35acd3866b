using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Helpers;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using System.Diagnostics;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyScheduleSynchronizationService : IApifyScheduleSynchronizationService
{
    private readonly ILogger<ApifyScheduleSynchronizationService> _logger;
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyWebhookService _webhookService;
    private readonly IScrapingConfigurationService _configurationService;

    public ApifyScheduleSynchronizationService(
        ILogger<ApifyScheduleSynchronizationService> logger,
        IApifyScheduleService scheduleService,
        IApifyTaskService taskService,
        IApifyWebhookService webhookService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _scheduleService = scheduleService;
        _taskService = taskService;
        _webhookService = webhookService;
        _configurationService = configurationService;
    }

    public async Task<ScheduleSynchronizationResult> SynchronizeAllSchedulesAsync(
        IEnumerable<JournalScheduleInfo> journals, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ScheduleSynchronizationResult();
        
        try
        {
            _logger.LogInformation("Starting synchronization of {JournalCount} journal schedules with APIFY", 
                journals.Count());

            var journalList = journals.ToList();
            result.JournalsProcessed = journalList.Count;

            // First, detect what changes are needed
            var changeDetection = await DetectScheduleChangesAsync(journalList, cancellationToken);
            
            // Validate for duplicates
            var duplicateValidation = await ValidateNoDuplicateSchedulesAsync(cancellationToken);
            if (duplicateValidation.HasDuplicates)
            {
                result.DuplicatesDetected = duplicateValidation.TotalDuplicates;
                result.Warnings.Add($"Found {duplicateValidation.TotalDuplicates} duplicate schedules that need resolution");
                
                // Log details about duplicates
                foreach (var duplicate in duplicateValidation.Duplicates)
                {
                    _logger.LogWarning("Duplicate schedules found for cron expression '{CronExpression}': {ScheduleIds}",
                        duplicate.CronExpression, string.Join(", ", duplicate.ScheduleIds));
                }
            }

            // Get webhook URL once for all operations
            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured - webhooks will not be created");
            }

            // Group journals by cron expression to optimize APIFY operations
            var journalGroups = journalList
                .Where(j => !string.IsNullOrEmpty(j.CronExpression))
                .GroupBy(j => j.CronExpression)
                .ToList();

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessJournalGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing journal group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("Synchronization completed in {Duration}ms. Created: {Created}, Updated: {Updated}, Skipped: {Skipped}, Errors: {Errors}",
                result.Duration.TotalMilliseconds, result.SchedulesCreated, result.SchedulesUpdated, result.SchedulesSkipped, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;
            
            var errorMessage = $"Failed to synchronize schedules: {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
            return result;
        }
    }

    public async Task<JournalSynchronizationResult> SynchronizeJournalScheduleAsync(
        JournalScheduleInfo journal, 
        CancellationToken cancellationToken = default)
    {
        var result = new JournalSynchronizationResult
        {
            JournalId = journal.Id,
            JournalName = journal.Name,
            CronExpression = journal.CronExpression
        };

        try
        {
            _logger.LogInformation("Synchronizing journal '{JournalName}' (ID: {JournalId}) with cron '{CronExpression}'",
                LogSanitizer.Sanitize(journal.Name), journal.Id, journal.CronExpression);

            // Get existing schedules from APIFY
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var existingSchedule = schedules?.Data.Items.Find(x => x.CronExpression == journal.CronExpression);

            if (existingSchedule != null)
            {
                // Schedule exists, check if we need to update the task
                var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(existingSchedule.Id, cancellationToken);
                
                if (taskIds == null || taskIds.Count == 0)
                {
                    // Schedule exists but no task - create task and link it
                    result.TaskId = await CreateTaskForJournal(journal, cancellationToken);
                    if (!string.IsNullOrEmpty(result.TaskId))
                    {
                        await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, result.TaskId, cancellationToken);
                        result.ScheduleId = existingSchedule.Id;
                        result.ActionTaken = SynchronizationAction.Updated;
                        result.Message = "Created missing task and linked to existing schedule";
                    }
                    else
                    {
                        result.ActionTaken = SynchronizationAction.Error;
                        result.Error = "Failed to create task for existing schedule";
                    }
                }
                else
                {
                    // Schedule and task exist - update task URLs if needed
                    result.TaskId = taskIds[0];
                    result.ScheduleId = existingSchedule.Id;
                    
                    await _taskService.UpdateTaskStartUrlsAsync(result.TaskId, result.ScheduleId, journal.Url, null, cancellationToken);
                    result.ActionTaken = SynchronizationAction.Updated;
                    result.Message = "Updated existing task URLs";
                }
            }
            else
            {
                // No existing schedule - create new one
                result.TaskId = await CreateTaskForJournal(journal, cancellationToken);
                if (!string.IsNullOrEmpty(result.TaskId))
                {
                    var scheduleName = ScrapingHelper.GenerateUniqueName("schedule", journal.CronExpression);
                    await _scheduleService.CreateScheduleForTaskAsync(result.TaskId, scheduleName, journal.CronExpression, cancellationToken);
                    
                    var webhookUrl = _configurationService.GetWebhookUrl();
                    if (!string.IsNullOrEmpty(webhookUrl))
                    {
                        await _webhookService.CreateWebhookForTaskAsync(result.TaskId, webhookUrl, cancellationToken);
                    }
                    
                    result.ActionTaken = SynchronizationAction.Created;
                    result.Message = "Created new schedule, task, and webhook";
                }
                else
                {
                    result.ActionTaken = SynchronizationAction.Error;
                    result.Error = "Failed to create task for new schedule";
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to synchronize journal '{journal.Name}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
            
            result.ActionTaken = SynchronizationAction.Error;
            result.Error = errorMessage;
            return result;
        }
    }

    private async Task<string?> CreateTaskForJournal(JournalScheduleInfo journal, CancellationToken cancellationToken)
    {
        var taskName = $"vigilit-{ScrapingHelper.SanitizeApifyName(journal.Name)}-{journal.Id}";

        try
        {
            var taskId = await _taskService.CreateTaskForJournalAsync(journal, taskName, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                _logger.LogWarning("Failed to create task for journal '{JournalName}': Task ID was null or empty",
                    LogSanitizer.Sanitize(journal.Name));
                return null;
            }
            return taskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create task '{TaskName}' for journal '{JournalName}': {Error}",
                taskName, LogSanitizer.Sanitize(journal.Name), ex.Message);
            return null;
        }
    }

    private async Task ProcessJournalGroup(
        IGrouping<string, JournalScheduleInfo> group,
        string webhookUrl,
        ScheduleSynchronizationResult result,
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();

        _logger.LogInformation("Processing journal group with cron '{CronExpression}' containing {JournalCount} journals",
            cronExpression, journals.Count);

        try
        {
            // Get existing schedules to check for duplicates and existing entries
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var existingSchedules = schedules?.Data.Items.Where(x => x.CronExpression == cronExpression).ToList() ?? new List<Apify.SDK.Model.GetListOfSchedulesResponseDataItems>();

            if (existingSchedules.Count > 1)
            {
                // Multiple schedules with same cron expression - this is a duplicate situation
                result.DuplicatesDetected += existingSchedules.Count - 1;
                result.Warnings.Add($"Found {existingSchedules.Count} schedules for cron '{cronExpression}' - will consolidate");

                // Use the first schedule and mark others for cleanup
                var primarySchedule = existingSchedules.OrderBy(s => s.CreatedAt).First();
                await ConsolidateSchedules(primarySchedule, existingSchedules.Skip(1), journals, result, cancellationToken);
                result.DuplicatesResolved += existingSchedules.Count - 1;
            }
            else if (existingSchedules.Count == 1)
            {
                // Single existing schedule - update it with current journals
                await UpdateExistingSchedule(existingSchedules[0], journals, webhookUrl, result, cancellationToken);
            }
            else
            {
                // No existing schedule - create new group schedule
                await CreateNewGroupSchedule(journals, cronExpression, webhookUrl, result, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            var errorMessage = $"Error processing journal group with cron '{cronExpression}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            result.Errors.Add(errorMessage);
        }
    }

    private async Task ConsolidateSchedules(
        Apify.SDK.Model.GetListOfSchedulesResponseDataItems primarySchedule,
        IEnumerable<Apify.SDK.Model.GetListOfSchedulesResponseDataItems> duplicateSchedules,
        List<JournalScheduleInfo> journals,
        ScheduleSynchronizationResult result,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Consolidating duplicate schedules for cron '{CronExpression}', keeping schedule '{ScheduleId}'",
            primarySchedule.CronExpression, primarySchedule.Id);

        // Update the primary schedule with all journal URLs
        var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(primarySchedule.Id, cancellationToken);
        if (taskIds != null && taskIds.Count > 0)
        {
            var primaryTaskId = taskIds[0];

            // Create group task with all journals
            var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-consolidated", primarySchedule.CronExpression);
            var newTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

            if (!string.IsNullOrEmpty(newTaskId))
            {
                await _scheduleService.UpdateScheduleAsync(primarySchedule.Id, newTaskId, cancellationToken);
                result.TasksCreated++;
                result.SchedulesUpdated++;
                result.Messages.Add($"Consolidated {journals.Count} journals into schedule '{primarySchedule.Id}'");
            }
        }

        // Note: In a production system, you might want to actually delete the duplicate schedules
        // For now, we just log them for manual cleanup
        foreach (var duplicate in duplicateSchedules)
        {
            _logger.LogWarning("Duplicate schedule '{ScheduleId}' should be manually removed", duplicate.Id);
            result.Messages.Add($"Duplicate schedule '{duplicate.Id}' marked for manual cleanup");
        }
    }

    private async Task UpdateExistingSchedule(
        Apify.SDK.Model.GetListOfSchedulesResponseDataItems existingSchedule,
        List<JournalScheduleInfo> journals,
        string webhookUrl,
        ScheduleSynchronizationResult result,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating existing schedule '{ScheduleId}' for cron '{CronExpression}' with {JournalCount} journals",
            existingSchedule.Id, existingSchedule.CronExpression, journals.Count);

        var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(existingSchedule.Id, cancellationToken);

        if (taskIds == null || taskIds.Count == 0)
        {
            // Schedule exists but no task - create new group task
            var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", existingSchedule.CronExpression);
            var taskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

            if (!string.IsNullOrEmpty(taskId))
            {
                await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, taskId, cancellationToken);

                if (!string.IsNullOrEmpty(webhookUrl))
                {
                    await _webhookService.CreateWebhookForTaskAsync(taskId, webhookUrl, cancellationToken);
                    result.WebhooksCreated++;
                }

                result.TasksCreated++;
                result.SchedulesUpdated++;
                result.Messages.Add($"Created task for existing schedule '{existingSchedule.Id}' with {journals.Count} journals");
            }
        }
        else
        {
            // Task exists - update URLs
            var taskId = taskIds[0];

            // For simplicity, recreate the group task with current journals
            // In a more sophisticated implementation, you might diff the URLs
            var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group-updated", existingSchedule.CronExpression);
            var newTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);

            if (!string.IsNullOrEmpty(newTaskId))
            {
                await _scheduleService.UpdateScheduleAsync(existingSchedule.Id, newTaskId, cancellationToken);
                result.TasksCreated++;
                result.SchedulesUpdated++;
                result.Messages.Add($"Updated schedule '{existingSchedule.Id}' with refreshed task containing {journals.Count} journals");
            }
        }
    }

    private async Task CreateNewGroupSchedule(
        List<JournalScheduleInfo> journals,
        string cronExpression,
        string webhookUrl,
        ScheduleSynchronizationResult result,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating new group schedule for cron '{CronExpression}' with {JournalCount} journals",
            cronExpression, journals.Count);

        var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", cronExpression);
        var groupScheduleName = ScrapingHelper.GenerateUniqueName("schedule-group", cronExpression);

        // Create group task
        var groupTaskId = await _taskService.CreateGroupTaskAsync(journals, groupTaskName, cancellationToken);
        if (string.IsNullOrEmpty(groupTaskId))
        {
            result.Errors.Add($"Failed to create group task for cron '{cronExpression}'");
            return;
        }

        result.TasksCreated++;
        result.Messages.Add($"Created group task '{groupTaskName}' for {journals.Count} journals");

        // Create schedule
        await _scheduleService.CreateScheduleForTaskAsync(groupTaskId, groupScheduleName, cronExpression, cancellationToken);
        result.SchedulesCreated++;
        result.Messages.Add($"Created schedule '{groupScheduleName}' for cron '{cronExpression}'");

        // Create webhook
        if (!string.IsNullOrEmpty(webhookUrl))
        {
            await _webhookService.CreateWebhookForTaskAsync(groupTaskId, webhookUrl, cancellationToken);
            result.WebhooksCreated++;
            result.Messages.Add($"Created webhook for task '{groupTaskId}'");
        }
    }

    public async Task<ScheduleChangeDetectionResult> DetectScheduleChangesAsync(
        IEnumerable<JournalScheduleInfo> journals,
        CancellationToken cancellationToken = default)
    {
        var result = new ScheduleChangeDetectionResult();

        try
        {
            _logger.LogInformation("Detecting schedule changes for {JournalCount} journals", journals.Count());

            var journalList = journals.ToList();
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var existingSchedules = schedules?.Data.Items ?? new List<Apify.SDK.Model.GetListOfSchedulesResponseDataItems>();

            // Group existing schedules by cron expression
            var schedulesByCron = existingSchedules.GroupBy(s => s.CronExpression).ToDictionary(g => g.Key, g => g.ToList());

            // Check each journal against existing schedules
            foreach (var journal in journalList)
            {
                if (string.IsNullOrEmpty(journal.CronExpression))
                {
                    result.Changes.Add(new ScheduleChange
                    {
                        JournalId = journal.Id,
                        JournalName = journal.Name,
                        ChangeType = ScheduleChangeType.ScheduleMissing,
                        Description = "Journal has no cron expression configured"
                    });
                    continue;
                }

                if (schedulesByCron.TryGetValue(journal.CronExpression, out var matchingSchedules))
                {
                    if (matchingSchedules.Count > 1)
                    {
                        result.DuplicateSchedules++;
                        result.Changes.Add(new ScheduleChange
                        {
                            JournalId = journal.Id,
                            JournalName = journal.Name,
                            ChangeType = ScheduleChangeType.CronExpressionChanged,
                            NewValue = journal.CronExpression,
                            Description = $"Multiple schedules found for cron expression (duplicates detected)"
                        });
                    }

                    // Check if tasks exist for the schedules
                    foreach (var schedule in matchingSchedules)
                    {
                        var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(schedule.Id, cancellationToken);
                        if (taskIds == null || taskIds.Count == 0)
                        {
                            result.Changes.Add(new ScheduleChange
                            {
                                JournalId = journal.Id,
                                JournalName = journal.Name,
                                ChangeType = ScheduleChangeType.TaskIdMissing,
                                Description = $"Schedule '{schedule.Id}' exists but has no associated task"
                            });
                        }
                    }
                }
                else
                {
                    result.NewSchedulesNeeded++;
                    result.Changes.Add(new ScheduleChange
                    {
                        JournalId = journal.Id,
                        JournalName = journal.Name,
                        ChangeType = ScheduleChangeType.NewJournal,
                        NewValue = journal.CronExpression,
                        Description = "New schedule needed for journal"
                    });
                }
            }

            // Check for orphaned schedules (schedules that don't match any journal)
            var journalCronExpressions = journalList.Select(j => j.CronExpression).Where(c => !string.IsNullOrEmpty(c)).ToHashSet();
            foreach (var schedule in existingSchedules)
            {
                if (!journalCronExpressions.Contains(schedule.CronExpression))
                {
                    result.OrphanedSchedules++;
                    result.Changes.Add(new ScheduleChange
                    {
                        JournalId = 0,
                        JournalName = "N/A",
                        ChangeType = ScheduleChangeType.JournalRemoved,
                        OldValue = schedule.CronExpression,
                        Description = $"Orphaned schedule '{schedule.Id}' with no matching journal"
                    });
                }
            }

            result.SchedulesToUpdate = result.Changes.Count(c =>
                c.ChangeType == ScheduleChangeType.CronExpressionChanged ||
                c.ChangeType == ScheduleChangeType.UrlChanged ||
                c.ChangeType == ScheduleChangeType.TaskIdMissing);

            _logger.LogInformation("Change detection completed. New: {New}, Updates: {Updates}, Orphaned: {Orphaned}, Duplicates: {Duplicates}",
                result.NewSchedulesNeeded, result.SchedulesToUpdate, result.OrphanedSchedules, result.DuplicateSchedules);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect schedule changes: {Error}", ex.Message);
            throw;
        }
    }

    public async Task<DuplicateValidationResult> ValidateNoDuplicateSchedulesAsync(CancellationToken cancellationToken = default)
    {
        var result = new DuplicateValidationResult();

        try
        {
            _logger.LogInformation("Validating for duplicate schedules in APIFY");

            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var existingSchedules = schedules?.Data.Items ?? new List<Apify.SDK.Model.GetListOfSchedulesResponseDataItems>();

            // Group schedules by cron expression to find duplicates
            var scheduleGroups = existingSchedules
                .GroupBy(s => s.CronExpression)
                .Where(g => g.Count() > 1)
                .ToList();

            foreach (var group in scheduleGroups)
            {
                var duplicateGroup = new DuplicateScheduleGroup
                {
                    CronExpression = group.Key,
                    ScheduleIds = group.Select(s => s.Id).ToList(),
                    FirstCreated = group.Min(s => s.CreatedAt ?? DateTime.MinValue),
                    LastCreated = group.Max(s => s.CreatedAt ?? DateTime.MinValue)
                };

                // Get task IDs for each schedule
                foreach (var schedule in group)
                {
                    var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(schedule.Id, cancellationToken);
                    if (taskIds != null && taskIds.Count > 0)
                    {
                        duplicateGroup.TaskIds.AddRange(taskIds);
                    }
                }

                result.Duplicates.Add(duplicateGroup);
            }

            _logger.LogInformation("Duplicate validation completed. Found {DuplicateGroups} groups with {TotalDuplicates} total duplicates",
                result.Duplicates.Count, result.TotalDuplicates);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate for duplicate schedules: {Error}", ex.Message);
            throw;
        }
    }

    public async Task<ScheduleCleanupResult> CleanupOrphanedSchedulesAsync(
        IEnumerable<JournalScheduleInfo> activeJournals,
        CancellationToken cancellationToken = default)
    {
        var result = new ScheduleCleanupResult();

        try
        {
            _logger.LogInformation("Starting cleanup of orphaned schedules. Active journals: {ActiveJournalCount}",
                activeJournals.Count());

            var journalList = activeJournals.ToList();
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var existingSchedules = schedules?.Data.Items ?? new List<Apify.SDK.Model.GetListOfSchedulesResponseDataItems>();

            // Get cron expressions from active journals
            var activeCronExpressions = journalList
                .Where(j => !string.IsNullOrEmpty(j.CronExpression))
                .Select(j => j.CronExpression)
                .ToHashSet();

            // Find orphaned schedules
            var orphanedSchedules = existingSchedules
                .Where(s => !activeCronExpressions.Contains(s.CronExpression))
                .ToList();

            result.OrphanedSchedulesFound = orphanedSchedules.Count;

            if (orphanedSchedules.Count == 0)
            {
                _logger.LogInformation("No orphaned schedules found");
                result.Messages.Add("No orphaned schedules found");
                return result;
            }

            _logger.LogInformation("Found {OrphanedCount} orphaned schedules to clean up", orphanedSchedules.Count);

            foreach (var orphanedSchedule in orphanedSchedules)
            {
                try
                {
                    _logger.LogInformation("Processing orphaned schedule '{ScheduleId}' with cron '{CronExpression}'",
                        orphanedSchedule.Id, orphanedSchedule.CronExpression);

                    // Get associated tasks
                    var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(orphanedSchedule.Id, cancellationToken);

                    if (taskIds != null && taskIds.Count > 0)
                    {
                        foreach (var taskId in taskIds)
                        {
                            _logger.LogInformation("Found associated task '{TaskId}' for orphaned schedule '{ScheduleId}'",
                                taskId, orphanedSchedule.Id);
                            // Note: In a production system, you might want to actually delete the task
                            // For now, we just log it for manual cleanup
                            result.Messages.Add($"Task '{taskId}' associated with orphaned schedule '{orphanedSchedule.Id}' should be manually reviewed");
                        }
                        result.TasksRemoved += taskIds.Count;
                    }

                    // Note: In a production system, you might want to actually delete the schedule
                    // For now, we just log it for manual cleanup to avoid accidental data loss
                    result.RemovedScheduleIds.Add(orphanedSchedule.Id);
                    result.Messages.Add($"Orphaned schedule '{orphanedSchedule.Id}' with cron '{orphanedSchedule.CronExpression}' marked for manual cleanup");

                    _logger.LogWarning("Orphaned schedule '{ScheduleId}' should be manually removed", orphanedSchedule.Id);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing orphaned schedule '{orphanedSchedule.Id}': {ex.Message}";
                    _logger.LogError(ex, "Error processing orphaned schedule '{ScheduleId}': {Error}",
                        orphanedSchedule.Id, ex.Message);
                    result.Errors.Add(errorMessage);
                }
            }

            // For safety, we're not actually deleting anything in this implementation
            // In a production system, you would add actual deletion logic here
            result.OrphanedSchedulesRemoved = 0; // Would be orphanedSchedules.Count if actually deleting

            _logger.LogInformation("Cleanup completed. Found: {Found}, Marked for removal: {MarkedForRemoval}, Errors: {Errors}",
                result.OrphanedSchedulesFound, result.RemovedScheduleIds.Count, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to cleanup orphaned schedules: {ex.Message}";
            _logger.LogError(ex, "Failed to cleanup orphaned schedules: {Error}", ex.Message);
            result.Errors.Add(errorMessage);
            return result;
        }
    }
}
