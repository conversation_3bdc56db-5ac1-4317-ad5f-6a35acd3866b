using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using System.Diagnostics;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class SynchronizeSchedulesCommandHandler : ISynchronizeSchedulesCommandHandler
{
    private readonly ILogger<SynchronizeSchedulesCommandHandler> _logger;
    private readonly IApifyScheduleSynchronizationService _synchronizationService;

    public SynchronizeSchedulesCommandHandler(
        ILogger<SynchronizeSchedulesCommandHandler> logger,
        IApifyScheduleSynchronizationService synchronizationService)
    {
        _logger = logger;
        _synchronizationService = synchronizationService;
    }

    public async Task<ScheduleSynchronizationResult> Consume(SynchronizeSchedulesCommand command, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("SynchronizeSchedulesCommandHandler: Starting synchronization of {JournalCount} journals. Type: {SyncType}, ValidationOnly: {ValidationOnly}",
                command.Journals.Count, command.SynchronizationType, command.ValidationOnly);

            var result = command.SynchronizationType switch
            {
                SynchronizationType.Full => await HandleFullSynchronization(command, cancellationToken),
                SynchronizationType.Incremental => await HandleIncrementalSynchronization(command, cancellationToken),
                SynchronizationType.ValidationOnly => await HandleValidationOnly(command, cancellationToken),
                SynchronizationType.CleanupOnly => await HandleCleanupOnly(command, cancellationToken),
                _ => throw new ArgumentException($"Unsupported synchronization type: {command.SynchronizationType}")
            };

            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;

            _logger.LogInformation("SynchronizeSchedulesCommandHandler: Completed in {Duration}ms. Success: {Success}, Errors: {ErrorCount}",
                result.Duration.TotalMilliseconds, !result.HasErrors, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var errorMessage = $"Failed to synchronize schedules: {ex.Message}";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, errorMessage);
            
            return new ScheduleSynchronizationResult
            {
                Duration = stopwatch.Elapsed,
                JournalsProcessed = command.Journals.Count,
                Errors = { errorMessage }
            };
        }
    }

    private async Task<ScheduleSynchronizationResult> HandleFullSynchronization(SynchronizeSchedulesCommand command, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Performing full synchronization");

        if (command.ValidationOnly)
        {
            return await HandleValidationOnly(command, cancellationToken);
        }

        var result = await _synchronizationService.SynchronizeAllSchedulesAsync(command.Journals, cancellationToken);

        // Perform cleanup if requested
        if (command.CleanupOrphanedSchedules && !result.HasErrors)
        {
            try
            {
                var cleanupResult = await _synchronizationService.CleanupOrphanedSchedulesAsync(command.Journals, cancellationToken);
                
                // Merge cleanup results
                result.Messages.AddRange(cleanupResult.Messages);
                result.Errors.AddRange(cleanupResult.Errors);
                
                if (cleanupResult.OrphanedSchedulesFound > 0)
                {
                    result.Messages.Add($"Cleanup: Found {cleanupResult.OrphanedSchedulesFound} orphaned schedules, marked {cleanupResult.RemovedScheduleIds.Count} for removal");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Cleanup phase failed but synchronization succeeded: {Error}", ex.Message);
                result.Warnings.Add($"Cleanup failed: {ex.Message}");
            }
        }

        return result;
    }

    private async Task<ScheduleSynchronizationResult> HandleIncrementalSynchronization(SynchronizeSchedulesCommand command, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Performing incremental synchronization");

        // First detect what changes are needed
        var changeDetection = await _synchronizationService.DetectScheduleChangesAsync(command.Journals, cancellationToken);
        
        if (!changeDetection.HasChanges)
        {
            _logger.LogInformation("No changes detected, skipping synchronization");
            return new ScheduleSynchronizationResult
            {
                JournalsProcessed = command.Journals.Count,
                SchedulesSkipped = command.Journals.Count,
                Messages = { "No changes detected - all schedules are up to date" }
            };
        }

        _logger.LogInformation("Detected {ChangeCount} changes to process", changeDetection.Changes.Count);

        // Only synchronize journals that have changes
        var journalsWithChanges = command.Journals
            .Where(j => changeDetection.Changes.Any(c => c.JournalId == j.Id))
            .ToList();

        if (command.ValidationOnly)
        {
            return new ScheduleSynchronizationResult
            {
                JournalsProcessed = command.Journals.Count,
                Messages = changeDetection.Changes.Select(c => c.Description).ToList()
            };
        }

        return await _synchronizationService.SynchronizeAllSchedulesAsync(journalsWithChanges, cancellationToken);
    }

    private async Task<ScheduleSynchronizationResult> HandleValidationOnly(SynchronizeSchedulesCommand command, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Performing validation-only synchronization");

        var result = new ScheduleSynchronizationResult
        {
            JournalsProcessed = command.Journals.Count
        };

        try
        {
            // Detect changes
            var changeDetection = await _synchronizationService.DetectScheduleChangesAsync(command.Journals, cancellationToken);
            result.Messages.AddRange(changeDetection.Changes.Select(c => $"[{c.ChangeType}] {c.JournalName}: {c.Description}"));

            // Validate for duplicates
            var duplicateValidation = await _synchronizationService.ValidateNoDuplicateSchedulesAsync(cancellationToken);
            if (duplicateValidation.HasDuplicates)
            {
                result.DuplicatesDetected = duplicateValidation.TotalDuplicates;
                result.Warnings.AddRange(duplicateValidation.Duplicates.Select(d => 
                    $"Duplicate schedules for cron '{d.CronExpression}': {string.Join(", ", d.ScheduleIds)}"));
            }

            // Summary
            result.Messages.Add($"Validation Summary: {changeDetection.NewSchedulesNeeded} new schedules needed, " +
                              $"{changeDetection.SchedulesToUpdate} updates required, " +
                              $"{changeDetection.OrphanedSchedules} orphaned schedules, " +
                              $"{duplicateValidation.TotalDuplicates} duplicates found");

            return result;
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Validation failed: {ex.Message}");
            return result;
        }
    }

    private async Task<ScheduleSynchronizationResult> HandleCleanupOnly(SynchronizeSchedulesCommand command, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Performing cleanup-only synchronization");

        var result = new ScheduleSynchronizationResult
        {
            JournalsProcessed = command.Journals.Count
        };

        try
        {
            var cleanupResult = await _synchronizationService.CleanupOrphanedSchedulesAsync(command.Journals, cancellationToken);
            
            // Convert cleanup result to synchronization result
            result.Messages.AddRange(cleanupResult.Messages);
            result.Errors.AddRange(cleanupResult.Errors);
            
            if (cleanupResult.OrphanedSchedulesFound > 0)
            {
                result.Messages.Add($"Found {cleanupResult.OrphanedSchedulesFound} orphaned schedules");
                result.Messages.Add($"Marked {cleanupResult.RemovedScheduleIds.Count} schedules for removal");
            }
            else
            {
                result.Messages.Add("No orphaned schedules found");
            }

            return result;
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Cleanup failed: {ex.Message}");
            return result;
        }
    }
}

public class SynchronizeJournalScheduleCommandHandler : ISynchronizeJournalScheduleCommandHandler
{
    private readonly ILogger<SynchronizeJournalScheduleCommandHandler> _logger;
    private readonly IApifyScheduleSynchronizationService _synchronizationService;

    public SynchronizeJournalScheduleCommandHandler(
        ILogger<SynchronizeJournalScheduleCommandHandler> logger,
        IApifyScheduleSynchronizationService synchronizationService)
    {
        _logger = logger;
        _synchronizationService = synchronizationService;
    }

    public async Task<JournalSynchronizationResult> Consume(SynchronizeJournalScheduleCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("SynchronizeJournalScheduleCommandHandler: Synchronizing journal '{JournalName}' (ID: {JournalId})",
                LogSanitizer.Sanitize(command.Journal.Name), command.Journal.Id);

            var result = await _synchronizationService.SynchronizeJournalScheduleAsync(command.Journal, cancellationToken);

            _logger.LogInformation("SynchronizeJournalScheduleCommandHandler: Completed for journal '{JournalName}'. Action: {Action}, Success: {Success}",
                LogSanitizer.Sanitize(command.Journal.Name), result.ActionTaken, result.IsSuccess);

            return result;
        }
        catch (Exception ex)
        {
            var errorMessage = $"Failed to synchronize journal '{command.Journal.Name}': {ex.Message}";
            _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
            
            return new JournalSynchronizationResult
            {
                JournalId = command.Journal.Id,
                JournalName = command.Journal.Name,
                CronExpression = command.Journal.CronExpression,
                ActionTaken = SynchronizationAction.Error,
                Error = errorMessage
            };
        }
    }
}
