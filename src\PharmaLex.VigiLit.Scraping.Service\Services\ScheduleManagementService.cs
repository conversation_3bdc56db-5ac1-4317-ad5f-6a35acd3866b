using Apify.SDK.Model;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Helpers;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ScheduleManagementService : IScheduleManagementService
{
    private readonly ILogger<ScheduleManagementService> _logger;
    private readonly IApifyScheduleService _scheduleService;
    private readonly IApifyTaskService _taskService;
    private readonly IApifyWebhookService _webhookService;
    private readonly IScrapingConfigurationService _configurationService;

    public ScheduleManagementService(
        ILogger<ScheduleManagementService> logger,
        IApifyScheduleService scheduleService,
        IApifyTaskService taskService,
        IApifyWebhookService webhookService,
        IScrapingConfigurationService configurationService)
    {
        _logger = logger;
        _scheduleService = scheduleService;
        _taskService = taskService;
        _webhookService = webhookService;
        _configurationService = configurationService;
    }

    public async Task<GetListOfSchedulesResponseDataItems?> FindExistingScheduleAsync(string cronExpression, CancellationToken cancellationToken = default)
    {
        var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
        return schedules?.Data.Items.Find(x => x.CronExpression == cronExpression);
    }

    public async Task<GetListOfSchedulesResponse?> GetAllSchedulesAsync(CancellationToken cancellationToken = default)
    {
        return await _scheduleService.GetSchedulesAsync(cancellationToken);
    }

    public async Task<string?> CreateScheduleWithTaskAsync(JournalScheduleInfo journal, CancellationToken cancellationToken = default)
    {
        try
        {
            var taskId = await CreateTaskForJournalAsync(journal, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                return null;
            }

            var scheduleName = ScrapingHelper.GenerateUniqueName("schedule", journal.CronExpression);
            await _scheduleService.CreateScheduleForTaskAsync(taskId, scheduleName, journal.CronExpression, cancellationToken);

            await CreateWebhookIfConfiguredAsync(taskId, cancellationToken);

            _logger.LogInformation("Created schedule with task for journal '{JournalName}' (ID: {JournalId})", 
                LogSanitizer.Sanitize(journal.Name), journal.Id);

            return taskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create schedule with task for journal '{JournalName}': {Error}",
                LogSanitizer.Sanitize(journal.Name), ex.Message);
            return null;
        }
    }

    public async Task<string?> CreateScheduleWithGroupTaskAsync(IEnumerable<JournalScheduleInfo> journals, string cronExpression, CancellationToken cancellationToken = default)
    {
        try
        {
            var journalList = journals.ToList();
            
            var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", cronExpression);
            var groupTaskId = await _taskService.CreateGroupTaskAsync(journalList, groupTaskName, cancellationToken);
            
            if (string.IsNullOrEmpty(groupTaskId))
            {
                _logger.LogWarning("Failed to create group task for cron '{CronExpression}': Task ID was null or empty", cronExpression);
                return null;
            }

            var groupScheduleName = ScrapingHelper.GenerateUniqueName("schedule-group", cronExpression);
            await _scheduleService.CreateScheduleForTaskAsync(groupTaskId, groupScheduleName, cronExpression, cancellationToken);

            await CreateWebhookIfConfiguredAsync(groupTaskId, cancellationToken);

            _logger.LogInformation("Created group schedule with task for cron '{CronExpression}' with {JournalCount} journals", 
                cronExpression, journalList.Count);

            return groupTaskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create group schedule with task for cron '{CronExpression}': {Error}",
                cronExpression, ex.Message);
            return null;
        }
    }

    public async Task<string?> UpdateScheduleTaskAsync(GetListOfSchedulesResponseDataItems schedule, string newUrl, string? oldUrl = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(schedule.Id, cancellationToken);
            
            if (taskIds == null || taskIds.Count == 0 || string.IsNullOrEmpty(taskIds[0]))
            {
                _logger.LogWarning("No existing task found for schedule '{ScheduleId}': Task ID was null or empty", schedule.Id);
                return null;
            }

            var taskId = taskIds[0];
            
            await _taskService.UpdateTaskStartUrlsAsync(taskId, schedule.Id, newUrl, oldUrl, cancellationToken);
            
            _logger.LogInformation("Updated task '{TaskId}' for schedule '{ScheduleId}' with new URL", taskId, schedule.Id);
            
            return taskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update task for schedule '{ScheduleId}': {Error}", schedule.Id, ex.Message);
            return null;
        }
    }

    public async Task<string?> UpdateScheduleWithGroupTaskAsync(GetListOfSchedulesResponseDataItems schedule, IEnumerable<JournalScheduleInfo> journals, CancellationToken cancellationToken = default)
    {
        try
        {
            var journalList = journals.ToList();
            
            var taskIds = await _taskService.GetTaskIdsByScheduleIdAsync(schedule.Id, cancellationToken);
            
            if (taskIds == null || taskIds.Count == 0 || string.IsNullOrEmpty(taskIds[0]))
            {
                var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group", schedule.CronExpression);
                var groupTaskId = await _taskService.CreateGroupTaskAsync(journalList, groupTaskName, cancellationToken);
                
                if (!string.IsNullOrEmpty(groupTaskId))
                {
                    await _scheduleService.UpdateScheduleAsync(schedule.Id, groupTaskId, cancellationToken);
                    await CreateWebhookIfConfiguredAsync(groupTaskId, cancellationToken);
                    
                    _logger.LogInformation("Created and linked group task '{GroupTaskName}' to existing schedule '{ScheduleId}' for {JournalCount} journals",
                        groupTaskName, schedule.Id, journalList.Count);
                    
                    return groupTaskId;
                }
                else
                {
                    _logger.LogWarning("Failed to create group task for existing schedule '{ScheduleId}'", schedule.Id);
                    return null;
                }
            }
            else
            {
                var groupTaskName = ScrapingHelper.GenerateUniqueName("vigilit-group-updated", schedule.CronExpression);
                var newTaskId = await _taskService.CreateGroupTaskAsync(journalList, groupTaskName, cancellationToken);
                
                if (!string.IsNullOrEmpty(newTaskId))
                {
                    await _scheduleService.UpdateScheduleAsync(schedule.Id, newTaskId, cancellationToken);
                    await CreateWebhookIfConfiguredAsync(newTaskId, cancellationToken);
                    
                    _logger.LogInformation("Updated existing schedule '{ScheduleId}' with refreshed group task '{GroupTaskName}' for {JournalCount} journals",
                        schedule.Id, groupTaskName, journalList.Count);
                    
                    return newTaskId;
                }
                else
                {
                    _logger.LogWarning("Failed to update group task for existing schedule '{ScheduleId}'", schedule.Id);
                    return taskIds[0];
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update schedule '{ScheduleId}' with group task: {Error}", schedule.Id, ex.Message);
            return null;
        }
    }

    public async Task<string?> CreateTaskForJournalAsync(JournalScheduleInfo journal, CancellationToken cancellationToken = default)
    {
        try
        {
            var taskName = $"vigilit-{ScrapingHelper.SanitizeApifyName(journal.Name)}-{journal.Id}";
            var taskId = await _taskService.CreateTaskForJournalAsync(journal, taskName, cancellationToken);
            
            if (string.IsNullOrEmpty(taskId))
            {
                _logger.LogWarning("Failed to create task for journal '{JournalName}': Task ID was null or empty", 
                    LogSanitizer.Sanitize(journal.Name));
                return null;
            }
            
            return taskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create task for journal '{JournalName}': {Error}",
                LogSanitizer.Sanitize(journal.Name), ex.Message);
            return null;
        }
    }

    public async Task<bool> CreateWebhookIfConfiguredAsync(string taskId, CancellationToken cancellationToken = default)
    {
        try
        {
            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                _logger.LogWarning("Webhook URL not configured - skipping webhook creation for task '{TaskId}'", taskId);
                return true; 
            }

            await _webhookService.CreateWebhookForTaskAsync(taskId, webhookUrl, cancellationToken);
            _logger.LogInformation("Created webhook for task '{TaskId}'", taskId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create webhook for task '{TaskId}': {Error}", taskId, ex.Message);
            return false;
        }
    }
}
