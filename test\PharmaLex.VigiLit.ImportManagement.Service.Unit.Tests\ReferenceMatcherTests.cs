﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Core.CrossCutting;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class ReferenceMatcherTests
{
    private readonly IReferenceMatcher _referenceMatcher;
    private readonly Mock<ICrossCuttingContext<ReferenceMatcher>> _mockCrossCutting = new();
    private readonly Mock<IAbstractTextSearcher> _mockAbstractTextSearcher = new();
    private readonly Mock<ILogger<ReferenceMatcher>> _mockLogger = new();
    private readonly MatchReference _matchReference;
    private readonly Mock<IScriptAdapter<bool>> _mockScriptAdapter = new();
    private readonly IReadOnlyCollection<string> _noJournalTitles = new List<string>();

    public ReferenceMatcherTests()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?> { { "DataExtraction:FuzzySharpJournalMatchThreshold", "90" }, })
            .Build();

        _mockCrossCutting.Setup(x => x.Configuration).Returns(configuration);
        _mockCrossCutting.Setup(x => x.Logger).Returns(_mockLogger.Object);

        _referenceMatcher = new ReferenceMatcher(_mockCrossCutting.Object, _mockAbstractTextSearcher.Object);
        _matchReference = new MatchReference()
        {
            JournalTitle = "--Journal--",
            AbstractText = "--ABSTRACT--",
            FullText = "--FULL--",
            FullUntranslatedText = "--UNTRANSLATED--",
        };
    }

    [Fact]
    public async Task ReferenceMatcher_MatchFound_ReturnsTrue()
    {
        // Arrange
        _mockAbstractTextSearcher.Setup(x => x.AbstractText).Returns(_matchReference.AbstractText);
        _mockScriptAdapter.Setup(x => x.RunAsync(It.Is<IAbstractTextSearcher>(m => m.AbstractText == "--ABSTRACT--"), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(true));

        // Act
        var result = await _referenceMatcher.Matches(_matchReference, _noJournalTitles, _mockScriptAdapter.Object);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchNotFound_ReturnsFalse()
    {
        // Arrange
        _mockAbstractTextSearcher.Setup(x => x.AbstractText).Returns(_matchReference.AbstractText);
        _mockScriptAdapter.Setup(x => x.RunAsync(It.Is<IAbstractTextSearcher>(m => m.AbstractText == "--ABSTRACT--"), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(false));

        // Act
        var result = await _referenceMatcher.Matches(_matchReference, _noJournalTitles, _mockScriptAdapter.Object);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchFoundWithJournalMatching_ReturnsTrue()
    {
        // Arrange
        _mockAbstractTextSearcher.SetupSequence(x => x.AbstractText)
            .Returns(_matchReference.AbstractText);

        _mockScriptAdapter.Setup(x => x.RunAsync(It.Is<IAbstractTextSearcher>(m => m.AbstractText == "--ABSTRACT--"), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(true));

        var journals = new List<string>()
        {
            "--Journal--",
        };

        // Act
        var result = await _referenceMatcher.Matches(_matchReference, journals, _mockScriptAdapter.Object);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchFoundButNotJournal_ReturnsFalse()
    {
        // Arrange
        _mockAbstractTextSearcher.SetupSequence(x => x.AbstractText)
            .Returns(_matchReference.AbstractText);

        _mockScriptAdapter.Setup(x => x.RunAsync(It.Is<IAbstractTextSearcher>(m => m.AbstractText == "--ABSTRACT--"), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(true));

        var journals = new List<string>()
        {
            "--Not this Journal--",
        };

        // Act
        var result = await _referenceMatcher.Matches(_matchReference, journals, _mockScriptAdapter.Object);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchingJournalWithDifferentCase_ReturnsTrue()
    {
        // Arrange
        _mockAbstractTextSearcher.SetupSequence(x => x.AbstractText)
            .Returns(_matchReference.AbstractText);

        _mockScriptAdapter.Setup(x => x.RunAsync(It.Is<IAbstractTextSearcher>(m => m.AbstractText == "--ABSTRACT--"), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(true));

        var journals = new List<string>()
        {
            "--JoUrNaL--",
        };

        // Act
        var result = await _referenceMatcher.Matches(_matchReference, journals, _mockScriptAdapter.Object);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ReferenceMatcher_MatchingJournalWithDifferentCaseAndExtraCharacters_ReturnsTrue()
    {
        // Arrange
        _mockAbstractTextSearcher.SetupSequence(x => x.AbstractText)
            .Returns(_matchReference.AbstractText);

        _mockScriptAdapter.Setup(x => x.RunAsync(It.Is<IAbstractTextSearcher>(m => m.AbstractText == "--ABSTRACT--"), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(true));

        var journals = new List<string>()
        {
            "--Journals--",
        };

        // Act
        var result = await _referenceMatcher.Matches(_matchReference, journals, _mockScriptAdapter.Object);

        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData(false, false, true)]
    [InlineData(false, true, false)]
    [InlineData(false, true, true)]
    [InlineData(true, false, false)]
    [InlineData(true, false, true)]
    [InlineData(true, true, false)]
    [InlineData(true, true, true)]
    public async Task ReferenceMatcher_MatchingJournal_And_At_least_one_text_Returns_True(bool isMatchingOnAbstract, bool isMatchingOnFull, bool isMatchingOnUntranslated)
    {
        // Arrange
        _mockAbstractTextSearcher.SetupSequence(x => x.AbstractText)
            .Returns(_matchReference.AbstractText)
            .Returns(_matchReference.FullText)
            .Returns(_matchReference.FullUntranslatedText);

        _mockScriptAdapter.Setup(s => s.RunAsync(It.IsAny<IAbstractTextSearcher>(), It.IsAny<CancellationToken>()))
            .Returns((IAbstractTextSearcher ats, CancellationToken _) =>
                ats.AbstractText switch
            {
                "--ABSTRACT--" => Task.FromResult(isMatchingOnAbstract),
                "--FULL--" => Task.FromResult(isMatchingOnFull),
                "--UNTRANSLATED--" => Task.FromResult(isMatchingOnUntranslated),
                _ => Task.FromResult(false)
            });

        var journals = new List<string>()
        {
            "--Journal--",
        };

        // Act
        var result = await _referenceMatcher.Matches(_matchReference, journals, _mockScriptAdapter.Object);

        // Assert
        Assert.True(result);
    }
}