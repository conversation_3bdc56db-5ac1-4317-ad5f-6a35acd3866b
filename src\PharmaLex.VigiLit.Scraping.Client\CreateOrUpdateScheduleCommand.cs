﻿using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Scraping.Client
{
    public class CreateOrUpdateScheduleCommand
    {
        public Journal NewJournal { get; set; } = new Journal();

        public Journal? OldJournal { get; set; } = new Journal();

        public Func<string, int, Task>? UpdateJournalWithTaskId { get; set; }

        public JournalOperationType OperationType { get; set; }
    }
}

