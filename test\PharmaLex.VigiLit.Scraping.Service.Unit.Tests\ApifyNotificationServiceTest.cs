﻿using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests;

public class ApifyNotificationServiceTest
{
    private readonly IApifyNotification _notificationService;
    private readonly Mock<ILogger<ApifyNotification>> _mockLogger = new();
    private readonly Mock<IApifyClient> _apifyClient = new();
    private readonly Mock<IDownloadBlobStorage> _blobStorage = new();
    private readonly Mock<IDataExtractionClient> _client = new();
    private readonly Mock<IApifyFileGroupingService> _metadataService = new();

    public ApifyNotificationServiceTest()
    {
        _notificationService = new ApifyNotification(_apifyClient.Object, _blobStorage.Object, _client.Object, _metadataService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task RunSucceedMethod_ValidPayload()
    {
        var runId = Fake.Apify.RunId;
        var mockFilePaths = Fake.Apify.BlobPaths();
        var payload = Fake.Apify.WebhookPayload(runId: runId);

        _blobStorage.Setup(x => x.GetBlobPathsAsync(
           runId,
           It.IsAny<CancellationToken>()
       ))
       .ReturnsAsync(mockFilePaths);

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
          .Returns(Task.CompletedTask);


        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(runId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, List<string>> { { "Default", mockFilePaths.ToList() } });

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _blobStorage.Verify(x => x.SetBlobFolderName(runId), Times.Once());
        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                payload.resource!.defaultDatasetId!,
                payload.resource.defaultKeyValueStoreId!,
                _blobStorage.Object,
                It.IsAny<IReadOnlyDictionary<string, string>>(),
                CancellationToken.None
            ),
            Times.Once()
        );

        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()),
                     Times.Exactly(2));

        foreach (var filePath in mockFilePaths)
        {
            _client.Verify(x => x.Send(It.Is<ExtractDataCommand>(cmd =>
                cmd.FileName == filePath &&
                cmd.Source == Source.File
            )), Times.Once());
        }
    }

    [Fact]
    public async Task RunSucceeded_WithValidData_SendsCorrectExtractDataCommands()
    {
        // Arrange
        var runId = Fake.Apify.RunId;
        var runData = Fake.Apify.WebhookPayload(runId: runId);

        var blobPaths = Fake.Apify.BlobPaths();

        _blobStorage.Setup(x => x.GetBlobPathsAsync(
           runId,
           It.IsAny<CancellationToken>()
       ))
       .ReturnsAsync(blobPaths);

        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(runId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Key-value store method not implemented"));

        var sentCommands = new List<ExtractDataCommand>();
        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
            .Callback<ExtractDataCommand>(cmd => sentCommands.Add(cmd))
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(runData);

        // Assert
        Assert.Equal(2, sentCommands.Count);

        var batchId = sentCommands[0].BatchId;
        Assert.All(sentCommands, cmd => Assert.Equal(batchId, cmd.BatchId));

        Assert.Contains(sentCommands, cmd => cmd.FileName == blobPaths[0]);
        Assert.Contains(sentCommands, cmd => cmd.FileName == blobPaths[1]);

        Assert.All(sentCommands, cmd => Assert.Equal(Source.File, cmd.Source));

        var file1Command = sentCommands.First(cmd => cmd.FileName == blobPaths[0]);
        var file2Command = sentCommands.First(cmd => cmd.FileName == blobPaths[1]);

        _client.Verify(x => x.Send(
            It.Is<ExtractDataCommand>(cmd =>
                cmd.FileName == file1Command.FileName &&
                cmd.Source == file1Command.Source &&
                cmd.BatchId == file1Command.BatchId &&
                cmd.CorrelationId == file1Command.CorrelationId)),
            Times.Once);

        _client.Verify(x => x.Send(
            It.Is<ExtractDataCommand>(cmd =>
                cmd.FileName == file2Command.FileName &&
                cmd.Source == file2Command.Source &&
                cmd.BatchId == file2Command.BatchId &&
                cmd.CorrelationId == file2Command.CorrelationId)),
            Times.Once);

        // Verify CorrelationIds are unique
        var correlationIds = sentCommands.Select(cmd => cmd.CorrelationId).ToList();
        Assert.Equal(correlationIds.Count, correlationIds.Distinct().Count());
    }

    [Theory]
    [InlineData("", "XYZ")]
    [InlineData("XYZ", "")]
    [InlineData("", "")]
    public async Task RunSucceedMethod_InvalidPayload_DoesNotCallTransferFiles(string defaultDatasetId, string defaultKeyValueStoreId)
    {
        var runId = Fake.Apify.RunId;
        var payload = Fake.Apify.WebhookPayload(datasetId: defaultDatasetId, keyValueStoreId: defaultKeyValueStoreId, runId: runId);

        await _notificationService.RunSucceeded(payload);

        _blobStorage.Verify(x => x.SetBlobFolderName(runId), Times.Never);

        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<IDownloadStorage>(),
                It.IsAny<IReadOnlyDictionary<string, string>>(),
                It.IsAny<CancellationToken>()
            ),
            Times.Never
        );

        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()),
                    Times.Never);
    }

    [Fact]
    public async Task RunSucceedMethod_EmptyFolder_DoesNotCallSend()
    {
        // Arrange
        var payload = new ApifyWebhookPayload { resource = new Resource() };
        _blobStorage.Setup(x => x.GetBlobPathsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync([]);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Never());
    }

    [Fact]
    public async Task RunFailed_ValidPayload_LogsError()
    {
        // Arrange
        var payload = Fake.Apify.WebhookPayload();

        // Act
        await _notificationService.RunFailed(payload);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("Scraping run failure")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task RunFailed_NullPayload_LogsError()
    {
        // Act
        await _notificationService.RunFailed(new ApifyWebhookPayload());

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("ApifyNotification: RunFailed called with insufficient run data")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task RunFailed_NullResource_LogsError()
    {
        // Arrange
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = null
        };

        // Act
        await _notificationService.RunFailed(payload);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("ApifyNotification: RunFailed called with insufficient run data")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task RunSucceeded_UsesKeyValueStoreMethod_WhenAvailable()
    {
        // Arrange
        var runId = Fake.Apify.RunId;
        var mockFilePaths = Fake.Apify.BlobPaths();
        var payload = Fake.Apify.WebhookPayload(runId: runId);

        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(runId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, List<string>> { { "TestJournal", mockFilePaths } });

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _metadataService.Verify(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(runId, It.IsAny<CancellationToken>()), Times.Once);
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Exactly(2));
    }

    [Fact]
    public async Task RunSucceeded_CreatesDefaultGroup_WhenKeyValueStoreMethodFails()
    {
        // Arrange
        var runId = Fake.Apify.RunId;
        var mockFilePaths = Fake.Apify.BlobPaths();
        var payload = Fake.Apify.WebhookPayload(runId: runId);

        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(runId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Key-value store error"));

        _blobStorage.Setup(x => x.GetBlobPathsAsync(runId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilePaths);

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _metadataService.Verify(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(runId, It.IsAny<CancellationToken>()), Times.Once);
        _blobStorage.Verify(x => x.GetBlobPathsAsync(runId, It.IsAny<CancellationToken>()), Times.Once);
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Exactly(2));
    }
}
