﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Ui.ViewModels.Countries;
using System.Text.Json;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

internal class JournalService(
    IJournalRepository journalRepository,
    IVigiLitScrapingClient scrapingClient,
    IMapper mapper) : IJournalService
{
    public async Task<IEnumerable<JournalViewModel>> GetAll()
    {
        var journals = await journalRepository.GetAll();

        foreach (var journal in journals)
        {
            journal.Issn = journal.Issn ?? "N/A";
            journal.CronExpression = GetCronType(journal.CronExpression);
        }

        return mapper.Map<IEnumerable<JournalViewModel>>(journals);

    }

    public async Task<IEnumerable<string>> GetNames()
    {
        var journals = await journalRepository.GetAll();
        return journals.Where(j => j.Enabled).Select(j => j.Name);
    }

    public async Task<JournalViewModel> GetJournal(int id)
    {
        var journal = await journalRepository.GetById(id);
        return mapper.Map<JournalViewModel>(journal);
    }

    public async Task<IEnumerable<JournalViewModel>> GetJournalsForCountry(int countryId)
    {
        var journals = await journalRepository.GetForCountry(countryId);
        return mapper.Map<IEnumerable<JournalViewModel>>(journals);
    }

    public async Task<IEnumerable<CountryModel>> GetSubscribedCountries()
    {
        var countries = await journalRepository.GetSubscribedCountries();
        return mapper.Map<IEnumerable<CountryModel>>(countries);
    }

    public async Task AddAsync(JournalModel model)
    {
        var journal = mapper.Map<Journal>(model);
        journalRepository.Add(journal);

        await journalRepository.SaveChangesAsync();
        var command = new CreateOrUpdateScheduleCommand()
        {
            NewJournal = journal,
            OldJournal = null,
            UpdateJournalWithTaskId = UpdateJournalWithTaskIdAsync,
            OperationType = JournalOperationType.Create
        };
        await scrapingClient.Send(command);
    }

    public async Task UpdateAsync(JournalModel model)
    {
        if (!model.Id.HasValue)
        {
            throw new ArgumentException("Id is required to update a journal");
        }

        var journal = await journalRepository.GetById(model.Id.Value);
        var oldJournal = JsonSerializer.Deserialize<Journal>(
            JsonSerializer.Serialize(journal)
        );

        mapper.Map(model, journal);
        await journalRepository.SaveChangesAsync();
        var command = new CreateOrUpdateScheduleCommand()
        {
            NewJournal = journal!,
            OldJournal = oldJournal,
            UpdateJournalWithTaskId = UpdateJournalWithTaskIdAsync,
            OperationType = JournalOperationType.Update
        };
        await scrapingClient.Send(command);
    }

    public async Task<int> CountContractsWithEnabledJournal(int id)
    {
        return await journalRepository.GetContractsWithEnabledJournalById(id);
    }
    public static string GetCronType(string cronExpression)
    {
        if (string.IsNullOrWhiteSpace(cronExpression))
            return "Invalid";

        var parts = cronExpression.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length != 6)
            return "Invalid";

        string dayOfMonth = parts[3];
        string dayOfWeek = parts[5];

        // Weekly: ? in day-of-month, specific numeric day-of-week (e.g., 0–6 or 0/2)
        if (dayOfMonth == "?" && (int.TryParse(dayOfWeek, out _) || dayOfWeek.Contains('/')))
            return "Weekly";

        // Monthly: ? in day-of-month and dayOfWeek has # or L (e.g., 2#1 or 5L)
        if (dayOfMonth == "?" && (dayOfWeek.Contains('#') || dayOfWeek.EndsWith("L", StringComparison.OrdinalIgnoreCase)))
            return "Monthly";

        // Monthly: Specific day-of-month and day-of-week is ?
        if (dayOfWeek == "?" && int.TryParse(dayOfMonth, out _))
            return "Monthly";

        return "Invalid";
    }

    private async Task UpdateJournalWithTaskIdAsync(string taskId, int id)
    {
        var journal = await journalRepository.GetById(id);
        if (journal != null)
        {
            journal.TaskId = taskId;
        }
        await journalRepository.SaveChangesAsync();
    }




}