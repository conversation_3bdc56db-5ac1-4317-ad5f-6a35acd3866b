﻿using Microsoft.Extensions.Options;
using Moq;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Import;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.PhlexVision;

public class ExtractDataFileDownloadTests
{
    private readonly IExtractDataFileDownload _extractDataFileDownload;

    private readonly Mock<IDocumentService> _mockDocumentService = new();
    private readonly IOptions<AzureStorageImportFileDocumentUploadOptions> _azureStorageImportFileDocumentUploadOptions = Options.Create(new AzureStorageImportFileDocumentUploadOptions() { AccountName = "Account", ContainerName = "UploadContainer" });
    private readonly IOptions<AzureStorageImportFileDocumentOptions> _azureStorageImportFileDocumentOptions = Options.Create(new AzureStorageImportFileDocumentOptions() { AccountName = "Account", ContainerName = "FileContainer" });

    public ExtractDataFileDownloadTests()
    {
        _extractDataFileDownload = new ExtractDataFileDownload(_mockDocumentService.Object, _azureStorageImportFileDocumentUploadOptions, _azureStorageImportFileDocumentOptions);
    }

    [Fact]
    public async Task Given_CreateTranslationBlobsAndGetPath_is_called_then_blobs_are_created_with_correct_metadata()
    {
        // Arrange
        var contextInfo = new ContextInfo
        {
            Language = "en",
            RawTranslatedText = "RawTranslatedText",
            RawUnTranslatedText = "RawUnTranslatedText",
        };
        var batchId = Guid.NewGuid();
        var fileName = "Filename.pdf";
        var translatedFileName = "Filename_TranslatedText.txt";
        var untranslatedFileName = "Filename_UntranslatedText.txt";

        _mockDocumentService.Setup(x => x.Create(
            It.IsAny<DocumentDescriptor>(),
            It.IsAny<Stream>(),
            It.IsAny<CancellationToken>()));

        // Act
        var blobStoragePath = await _extractDataFileDownload.CreateTranslationBlobsAndGetPath(batchId, fileName, contextInfo);

        // Assert
        _mockDocumentService.Verify(x => x.Create(
            It.Is<DocumentDescriptor>(d => d.BlobName == $"{batchId}/{translatedFileName}"),
            It.Is<Stream>(s => StreamContainsText(s, contextInfo.RawTranslatedText)),
            It.IsAny<CancellationToken>()
        ), Times.Once);

        _mockDocumentService.Verify(x => x.Create(
            It.Is<DocumentDescriptor>(d => d.BlobName == $"{batchId}/{untranslatedFileName}"),
            It.Is<Stream>(s => StreamContainsText(s, contextInfo.RawUnTranslatedText)),
            It.IsAny<CancellationToken>()
        ), Times.Once);

        Assert.Equal(batchId, blobStoragePath.BatchId);
        Assert.Equal(fileName, blobStoragePath.FileName);
        Assert.Equal(translatedFileName, blobStoragePath.TranslatedFileName);
        Assert.Equal(untranslatedFileName, blobStoragePath.UntranslatedFileName);
    }
    private static bool StreamContainsText(Stream stream, string expectedText)
    {
        if (stream == null) return false;

        stream.Position = 0;
        using var reader = new StreamReader(stream, leaveOpen: true);
        var content = reader.ReadToEnd();
        stream.Position = 0;

        return content == expectedText;
    }


}