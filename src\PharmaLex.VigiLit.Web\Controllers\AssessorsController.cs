﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Domain.UserManagement;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace PharmaLex.VigiLit.Web.Controllers;

[Route("[controller]")]
[Authorize(Policy = Policies.Admin)]
public class AssessorsController : BaseController
{
    private readonly IUserService _userService;
    private readonly ILogger<AssessorsController> _logger;

    public AssessorsController(IUserService userService,
                                ILogger<AssessorsController> logger,
                                IUserSessionService userSessionService,
                                IConfiguration configuration) : base(userSessionService, configuration)
    {
        _userService = userService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var users = await _userService.GetAssessorsAsync();

        return View(users);
    }

    [HttpGet("edit/{id}")]
    public async Task<IActionResult> Edit(int id)
    {
        var assessor = await _userService.GetAssessorAsync(id);

        return View(assessor);
    }

    [HttpPost("edit/{id}")]
    public async Task<IActionResult> Edit(int id, List<int> selectedSubstanceIds, int qualityCheckPercentage)
    {
        try
        {
            await _userService.UpdateAssessor(id, selectedSubstanceIds, qualityCheckPercentage);
            AddNotification("Assessor was edited successfully. ", UserNotificationType.Confirm);
            return RedirectToAction("Index");
        }
        catch (NotFoundException ex)
        {
            _logger.LogError(ex, "Could not find a Assessor with ID : '{Id}'", id);
            return NotFound("Something went wrong, please try again.");
        }
    }
}