﻿using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Scraping.Service.Helpers;

public static partial class ScrapingHelper
{
    public static string SanitizeApifyName(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return "unknown";
        }

        var sanitized = input.ToLowerInvariant();

        sanitized = ApifyName().Replace(sanitized, "-");

        sanitized = sanitized.Trim('-');

        while (sanitized.Contains("--"))
        {
            sanitized = sanitized.Replace("--", "-");
        }

        sanitized = sanitized.Trim('-');

        return string.IsNullOrEmpty(sanitized) ? "unknown" : sanitized;
    }

    public static string GenerateUniqueName(string prefix, string cronExpression)
    {
        var sanitizedCron = ScrapingHelper.SanitizeApifyName(cronExpression);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
        var uniqueId = Guid.NewGuid().ToString("N")[..8];

        return $"{prefix}-{sanitizedCron}-{timestamp}-{uniqueId}";
    }

    [GeneratedRegex(@"[^a-zA-Z0-9]")]
    private static partial Regex ApifyName();
}