using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public class ImportSelectionRepository : TrackingGenericRepository<ImportSelection>, IImportSelectionRepository
{
    protected readonly IMapper _mapper;
    private readonly ILogger<ImportSelectionRepository> _logger;

    public ImportSelectionRepository(PlxDbContext context, IMapper mapper, IUserContext userContext, ILoggerFactory loggerFactory) : base(context, userContext.User)
    {
        _mapper = mapper;
        _logger = loggerFactory.CreateLogger<ImportSelectionRepository>();
    }

    public async Task Select(int userId, int importId)
    {
        var selections = await context.Set<ImportSelection>()
            .Where(i => i.UserId == userId)
            .ToListAsync();

        if (selections.Count == 0)
        {
            Add(new ImportSelection(userId, importId));
        }
        else if (selections.Count == 1)
        {
            selections[0].ImportId = importId;
        }
        else if (selections.Count > 1)
        {
            string importIds = string.Join(",", selections.Select(f => f.ImportId));
            _logger.LogWarning("User {UserId} is selecting import {ImportId} but multiple selections were found. importIds={ImportIds}", userId, importId, importIds);

            RemoveRange(selections);
            Add(new ImportSelection(userId, importId));
        }

        await SaveChangesAsync();
    }

    public async Task Deselect(int userId, int importId)
    {
        var selections = await context.Set<ImportSelection>()
            .Where(i => i.UserId == userId)
            .ToListAsync();

        if (selections.Count > 1)
        {
            string importIds = string.Join(",", selections.Select(f => f.ImportId));
            _logger.LogWarning("User {UserId} is deselecting import {ImportId} but multiple selections were found. importIds={ImportIds}", userId, importId, importIds);
        }

        RemoveRange(selections);
        await SaveChangesAsync();
    }

    public async Task<ImportSelectionModel?> GetSelectedImport(int userId)
    {
        var query = context.Set<ImportSelection>()
            .Include(i => i.Import)
            .Where(i => i.UserId == userId)
            .AsNoTracking();

        return await _mapper.ProjectTo<ImportSelectionModel?>(query).FirstOrDefaultAsync();
    }

    public async Task ClearSelections(int importId)
    {
        var selections = await context.Set<ImportSelection>()
            .Where(i => i.ImportId == importId)
            .ToListAsync();

        RemoveRange(selections);

        await SaveChangesAsync();
    }
}