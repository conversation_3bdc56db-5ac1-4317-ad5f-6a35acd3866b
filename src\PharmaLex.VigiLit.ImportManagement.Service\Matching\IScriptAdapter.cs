﻿using Microsoft.CodeAnalysis.Scripting;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;
internal interface IScriptAdapter<T>
{
    /// <summary>
    /// Runs the script from the beginning.
    /// </summary>
    /// <param name="globals">
    ///     An instance of <see cref="Script.GlobalsType"/> holding on values for global variables accessible from the script.
    ///     Must be specified if and only if the script was created with <see cref="Script.GlobalsType"/>.
    /// </param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>A <see cref="ScriptState"/> that represents the state after running the script, including all declared variables and return value.</returns>
    /// <exception cref="CompilationErrorException">Compilation has errors.</exception>
    /// <exception cref="ArgumentException">The type of <paramref name="globals"/> doesn't match <see cref="Script.GlobalsType"/>.</exception>
    Task<T> RunAsync(object globals, CancellationToken cancellationToken = default);
}
