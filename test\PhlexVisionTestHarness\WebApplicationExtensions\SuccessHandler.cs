﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using System.Text.Json;
using Console = PhlexVisionTestHarness.CrossCutting.ConsoleEx;

namespace PhlexVisionTestHarness.WebApplicationExtensions;

internal static class SuccessHandler
{
    private const string CorrelationIdHeader = "X-Correlation-ID";

    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    public static void AddSuccessHandler(this WebApplication app, ICallbackHandler responseHandler, IMetaDataExtractor metaDataExtractor)
    {
        app.MapPost("/success/VigiLit", async (HttpContext context) =>
        {
            var correlationIdHeader = context.Request.Headers[CorrelationIdHeader];
            if (!Guid.TryParse(correlationIdHeader, out var correlationId))
            {
                return Results.BadRequest("Invalid or missing Correlation ID");
            }

            using var reader = new StreamReader(context.Request.Body);
            var body = await reader.ReadToEndAsync();
            Console.WriteLine(ConsoleColor.Magenta, body + $"\n{correlationId}");

            ExtractedMetadata? extractedMetadata;
            extractedMetadata = GetExtractedMetadata(body, correlationId);

            Console.WriteLine(ConsoleColor.Green, $"Success CorrelationId: {correlationId}");

            if (extractedMetadata == null)
            {
                Console.WriteLine(ConsoleColor.Red, $"{correlationId}: Could not extract metadata");
            }

#pragma warning disable S125 // Remove this commented out code. - its explaining what to uncomment to test it further

            // Now we would want to call the follow:
            // await responseHandler.Success(correlationId, extractedMetadata, contextInfo);

#pragma warning restore S125 // Remove this commented out code. - its explaining what to uncomment to test it further

            return Results.Ok("Callback Success received");
        });
    }

    private static ExtractedMetadata? GetExtractedMetadata(string body, Guid correlationId)
    {
        ExtractedMetadata? extractedMetadata;
        var rootObject = JsonSerializer.Deserialize<RootObjectMultiPrompt>(body, JsonOptions);

        if (rootObject == null)
        {
            Console.WriteLine(ConsoleColor.Red, $"{correlationId}: Root object for multi prompt is null");
        }

        var aiProcessing = rootObject?.AiProcessing;

        if (aiProcessing == null)
        {
            Console.WriteLine(ConsoleColor.Red, $"{correlationId}: AiProcessing object for multi prompt is null");
        }

        extractedMetadata = aiProcessing?.MetadataExtraction;
        return extractedMetadata;
    }
}