﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Text;

namespace PharmaLex.VigiLit.Infrastructure.Emails;

public class SendGridSender : IEmailSender
{
    private readonly ILogger<SendGridSender> _logger;
    private readonly ISendGridClient _client;
    private readonly EmailOptions _emailOptions;

    public SendGridSender(
        ILoggerFactory loggerFactory,
        ISendGridClient sendGridClient,
        IOptions<EmailOptions> emailOptions)
    {
        _logger = loggerFactory.CreateLogger<SendGridSender>();
        _client = sendGridClient;
        _emailOptions = emailOptions.Value;
    }

    public async Task<Response> SendDailyReferenceClassificationEmail(DailyClassificationEmailSendGridTemplateModel templateData)
    {
        var from = new EmailAddress(_emailOptions.NoReplyEmail, _emailOptions.NoReplyEmailName);
        var to = new EmailAddress(templateData.RecipientEmail, templateData.RecipientName);

        var msg = new SendGridMessage();
        msg.SetFrom(from);
        msg.AddTo(to);
        msg.SetTemplateId(_emailOptions.DailyReferenceClassificationEmailTemplateId);
        msg.SetTemplateData(templateData);

        return await SendEmailAndLogFailure("SendDailyReferenceClassificationEmail", msg);
    }

    public async Task<Response> SendInvitationEmail(InvitationEmailModel model)
    {
        var from = new EmailAddress(_emailOptions.NoReplyEmail, _emailOptions.NoReplyEmailName);
        var to = new EmailAddress(model.CompanyUser.Email, model.CompanyUser.DisplayFullName);

        var msg = new SendGridMessage();
        msg.SetFrom(from);
        msg.AddTo(to);
        msg.SetTemplateId(_emailOptions.InvitationEmailTemplateId);
        msg.SetSubject(model.Subject);
        msg.SetTemplateData(model);

        return await SendEmailAndLogFailure("SendInvitationEmail", msg);
    }

    public async Task<Response> SendCaseEmail(CaseEmailModel caseEmail, string fileName, byte[] zippedAttachment)
    {
        var from = new EmailAddress(_emailOptions.NoReplyEmail, _emailOptions.NoReplyEmailName);
        var to = new EmailAddress(caseEmail.RecipientEmail, caseEmail.RecipientName);

        var msg = new SendGridMessage();
        msg.SetFrom(from);
        msg.AddTo(to);
        msg.SetTemplateId(_emailOptions.CaseEmailTemplateId);
        msg.SetSubject(caseEmail.Subject);
        msg.SetTemplateData(caseEmail);

        if (zippedAttachment.Length > 0)
        {
            using (var attachmentStream = new MemoryStream())
            {
#pragma warning disable CA1835 
                await attachmentStream.WriteAsync(zippedAttachment, 0, zippedAttachment.Length);
#pragma warning restore CA1835 
                attachmentStream.Position = 0;

                await msg.AddAttachmentAsync(fileName, attachmentStream, "application/zip");
            }
        }

        return await SendEmailAndLogFailure("SendCaseEmail", msg);
    }

    private async Task<Response> SendEmailAndLogFailure(string caller, SendGridMessage msg)
    {
        await RateLimiter();

        var response = await _client.SendEmailAsync(msg);

        // send failed, log useful info
        if (!response.IsSuccessStatusCode)
        {
            // response body json
            var body = await response.DeserializeResponseBodyAsync();

            // body is key value pairs, concat them
            var bodyStringBuilder = new StringBuilder();

            foreach (var key in body.Keys)
            {
                if (bodyStringBuilder.Length > 0)
                {
                    bodyStringBuilder.Append('\n');
                }

                bodyStringBuilder.Append($"{key}: {body[key]}");
            }

            // the response refers to fields in the message, so we have to log those too
            var messageStringBuilder = new StringBuilder();

            foreach (var personalization in msg.Personalizations)
            {
                if (messageStringBuilder.Length > 0)
                {
                    messageStringBuilder.Append('\n');
                }

                // recipients with their index number
                var tosStringBuilder = new StringBuilder();

                for (int i = 0; i < personalization.Tos.Count; i++)
                {
                    if (tosStringBuilder.Length > 0)
                    {
                        tosStringBuilder.Append("; ");
                    }

                    tosStringBuilder.Append($"[{i}] {personalization.Tos[i].Name} <{personalization.Tos[i].Email}>");
                }

                messageStringBuilder.Append($"To: {tosStringBuilder.ToString()}");
            }

            _logger.LogWarning("{Caller} email failed to send.\n{Message}\n{ResponseBody}", caller, messageStringBuilder.ToString(), bodyStringBuilder.ToString());
        }

        return response;
    }

    private static async Task RateLimiter()
    {
        // SendGrid's approach:
        // - All calls within the Web API are allotted a specific number of requests per refresh period.
        // - Each Web API request returns the following header information regarding rate limits and number of requests left.
        // - Depending on the endpoint you are trying to reach, it will have a specific number of allowed requests per refresh period.
        //   Once this threshold has been reached, we will return a status code 429 response.

        // Our approach: 
        // - Don't get blocked.
        // - Be a good ecosystem player.

        await Task.Delay(100);
    }
}
