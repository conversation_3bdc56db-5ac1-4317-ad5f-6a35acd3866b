﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AddRazorSupportForMvc>true</AddRazorSupportForMvc>
		<EnableDefaultContentItems>false</EnableDefaultContentItems>
		<OutputType>Library</OutputType>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Import\**" />
		<Compile Remove="Views\Shared\Components\Vue3\**" />
		<EmbeddedResource Remove="Import\**" />
		<EmbeddedResource Remove="Views\Shared\Components\Vue3\**" />
		<None Remove="Import\**" />
		<None Remove="Views\Shared\Components\Vue3\**" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj" />
		<ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.ContractManagement.Ui\PharmaLex.VigiLit.ContractManagement.Ui.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Client\PharmaLex.VigiLit.DataExtraction.Client.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Documents\PharmaLex.VigiLit.ImportManagement.Documents.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Entities\PharmaLex.VigiLit.ImportManagement.Entities.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
		<PackageReference Include="Microsoft.FeatureManagement" Version="4.2.1" />
		<PackageReference Include="PharmaLex.Caching.Data" Version="8.0.0.202" />
		<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
	</ItemGroup>

  <ItemGroup>
    <Folder Include="Dashboard\" />
  </ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Views\**\*.cshtml" />
	</ItemGroup>
	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
	</ItemGroup>
	<ItemGroup>
	  <EmbeddedResource Update="Views\Import\AdHocList.cshtml">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	</ItemGroup>

</Project>
